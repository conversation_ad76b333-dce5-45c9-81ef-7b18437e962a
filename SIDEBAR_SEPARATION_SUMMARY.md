# Sidebar Separation - Admin vs Contractor

This document outlines the changes made to separate admin and contractor sidebar functionality.

## Problem

The original sidebar was mixing admin and contractor functionality, causing confusion where admin users would be redirected to contractor login when logging out.

## Solution

Created separate sidebar components for different user roles:

### 1. New Components Created

#### AdminSidebar (`src/components/admin-sidebar.tsx`)

- Dedicated sidebar for admin/JKR users
- Admin-specific menu items:
  - Dashboard
  - Projects (admin view)
  - Contractors (management)
  - Competent Persons (management)
  - User Management
  - Settings
- Handles admin logout correctly (redirects to `/admin/login`)
- Shows "Admin Panel" subtitle

#### ContractorSidebar (`src/components/contractor-sidebar.tsx`)

- Dedicated sidebar for contractor users
- Maintains existing contractor functionality:
  - Project context switching
  - Dynamic menu items based on project context
  - General contractor menu items (Projects, Profile, CP List)
  - Project-specific menu items (Dashboard, PMAs, Maintenance Logs, Complaints, Members)
- Handles contractor logout correctly (redirects to `/contractor/login`)
- Shows "Contractor Portal" subtitle

### 2. Updated Main Sidebar (`src/components/sidebar.tsx`)

- Now acts as a role-based sidebar selector
- Uses `usePermissions` hook to determine user role
- Renders appropriate sidebar component:
  - `AdminSidebar` for JKR/admin users
  - `ContractorSidebar` for contractor users
- Shows loading state while determining user role

### 3. Translation Updates

#### English (`messages/en.json`)

Added new navigation keys:

- `adminPanel`: "Admin Panel"
- `competentPersons`: "Competent Persons"
- `userManagement`: "User Management"

#### Malay (`messages/ms.json`)

Added corresponding translations:

- `adminPanel`: "Panel Pentadbir"
- `competentPersons`: "Orang Kompeten"
- `userManagement`: "Pengurusan Pengguna"

## Benefits

1. **Clear Separation of Concerns**: Admin and contractor functionality are now completely separate
2. **Correct Logout Behavior**: Each role redirects to their appropriate login page
3. **Role-Specific UI**: Each sidebar shows only relevant menu items for that role
4. **Maintainability**: Easier to add role-specific features without affecting other roles
5. **User Experience**: Clear indication of which portal the user is in (Admin Panel vs Contractor Portal)

## Usage

The main `AppSidebar` component automatically determines which sidebar to show based on user role. No changes needed in existing code that uses `<AppSidebar />`.

## Future Enhancements

- Could add viewer-specific sidebar if needed
- Menu items can be easily configured per role
- Permission-based menu item filtering can be added within each role-specific sidebar
