'use client';

import { usePermissions } from '@/hooks/use-permissions';
import { UnifiedSidebar } from './unified-sidebar';
// import { CustomSidebar } from './custom-sidebar';

/**
 * Main sidebar component that renders the custom sidebar for all roles
 */
export function AppSidebar() {
  const { isLoading } = usePermissions();

  // Show loading state while determining user role
  if (isLoading) {
    return (
      <div className="flex h-screen w-64 border-r bg-background">
        <div className="flex flex-col items-center justify-center w-full">
          <div className="animate-pulse">
            <div className="h-8 w-8 bg-muted rounded-lg mb-2"></div>
            <div className="h-4 w-20 bg-muted rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  // Use custom sidebar for all roles
  return <UnifiedSidebar />;
}
