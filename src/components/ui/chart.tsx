'use client';

import { cn } from '@/lib/utils';
import { type VariantProps, cva } from 'class-variance-authority';
import * as React from 'react';
import { Tooltip } from 'recharts';
import type { TooltipProps } from 'recharts/types/component/Tooltip';

type RecursivePartial<T> = {
  [P in keyof T]?: T[P] extends (infer U)[]
    ? RecursivePartial<U>[]
    : T[P] extends object
      ? RecursivePartial<T[P]>
      : T[P];
};

// Define chart colors as CSS variables
const chartColors = {
  chart1: 'hsl(215, 100%, 75%)', // Pastel blue
  chart2: 'hsl(151, 100%, 80%)', // Pastel green
  chart3: 'hsl(46, 100%, 80%)', // Pastel yellow
  chart4: 'hsl(338, 100%, 85%)', // Pastel pink
  chart5: 'hsl(262, 100%, 85%)', // Pastel purple
  chart6: 'hsl(16, 100%, 80%)', // Pastel orange
};

export type ChartConfig = Record<
  string,
  {
    label: string;
    color: string;
  }
>;

// Chart Container
const chartContainerVariants = cva('relative', {
  variants: {
    size: {
      sm: 'h-[200px]',
      md: 'h-[300px]',
      lg: 'h-[400px]',
      xl: 'h-[500px]',
    },
  },
  defaultVariants: {
    size: 'md',
  },
});

interface ChartContainerProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof chartContainerVariants> {
  config: ChartConfig;
}

export function ChartContainer({
  children,
  config,
  className,
  size,
  ...props
}: ChartContainerProps) {
  // Set colors as CSS variables
  const style = React.useMemo(() => {
    const style: Record<string, string> = {};

    Object.entries(config).forEach(([key, value]) => {
      style[`--color-${key}`] = value.color;
    });

    Object.entries(chartColors).forEach(([key, value]) => {
      style[`--${key}`] = value;
    });

    return style;
  }, [config]);

  return (
    <div
      className={cn(chartContainerVariants({ size }), className)}
      style={style}
      {...props}
    >
      {children}
    </div>
  );
}

// Chart Legend
interface ChartLegendProps {
  className?: string;
  content?: React.ReactNode;
}

export function ChartLegend({
  className,
  content,
  ...props
}: ChartLegendProps) {
  return (
    <div
      className={cn('mt-4 flex flex-wrap items-center gap-4', className)}
      {...props}
    >
      {content}
    </div>
  );
}

// Chart Legend Content
interface ChartLegendContentProps extends React.HTMLAttributes<HTMLDivElement> {
  payload?: Array<{
    color: string;
    value: string;
    payload: {
      fill: string;
      stroke: string;
      dataKey: string;
    };
  }>;
}

export function ChartLegendContent({
  payload,
  className,
  ...props
}: ChartLegendContentProps) {
  if (!payload || payload.length === 0) {
    return null;
  }

  return (
    <div
      className={cn('flex flex-wrap items-center gap-4', className)}
      {...props}
    >
      {payload.map((entry, index) => {
        const _dataKey = entry.payload.dataKey;
        const color = entry.payload.stroke || entry.payload.fill;

        return (
          <div key={`item-${index}`} className="flex items-center gap-2">
            <div
              className="h-3 w-3 rounded-sm"
              style={{
                backgroundColor: color,
              }}
            />
            <span className="text-sm font-medium">{entry.value}</span>
          </div>
        );
      })}
    </div>
  );
}

// Chart Tooltip
interface ChartTooltipProps
  extends RecursivePartial<TooltipProps<number, string>> {
  className?: string;
}

export function ChartTooltip({
  className: _className,
  content,
  cursor = false,
  ...props
}: ChartTooltipProps) {
  // Using type assertion to resolve the type incompatibility issues between our component and recharts
  // This is necessary due to complex generic types in Recharts that don't align perfectly with our component props

  // Using Record<string, unknown> for better type safety while maintaining compatibility
  return (
    <Tooltip
      content={content as TooltipProps<number, string>['content']}
      cursor={cursor as TooltipProps<number, string>['cursor']}
      isAnimationActive={false}
      {...(props as Record<string, unknown>)}
      {...(props as Omit<TooltipProps<number, string>, 'content' | 'cursor'>)}
    />
  );
}

// Chart Tooltip Content
export interface ChartTooltipContentProps
  extends React.HTMLAttributes<HTMLDivElement> {
  active?: boolean;
  payload?: Array<{
    name: string;
    value: number;
    color: string;
    dataKey: string;
    payload?: Record<string, unknown>; // More specific than 'any' for chart data
  }>;
  label?: string;
  indicator?: 'dot' | 'line';
  indicatorClassName?: string;
  formatter?: (
    value: number,
    name: string,
    item: ChartDataItem,
  ) => React.ReactNode;
  labelFormatter?: (label: string) => React.ReactNode;
}

// Type for chart data items to replace 'any'
type ChartDataItem = {
  name: string;
  value: number;
  color: string;
  dataKey: string;
  payload?: Record<string, unknown>;
};

export function ChartTooltipContent({
  active,
  payload,
  label,
  className,
  indicator = 'dot',
  indicatorClassName,
  formatter,
  labelFormatter,
  ...props
}: ChartTooltipContentProps) {
  if (!active || !payload?.length) {
    return null;
  }

  return (
    <div
      className={cn('rounded-lg border bg-background p-2 shadow-sm', className)}
      {...props}
    >
      {label && (
        <div className="mb-1 text-sm font-medium">
          {labelFormatter ? labelFormatter(label) : label}
        </div>
      )}
      <div className="flex flex-col gap-0.5">
        {payload.map((item, index) => {
          const formattedValue = formatter
            ? formatter(item.value, item.name, item)
            : item.value;

          return (
            <div key={index} className="flex items-center gap-1.5">
              {indicator === 'dot' && (
                <div
                  className={cn('h-2 w-2 rounded-full', indicatorClassName)}
                  style={{
                    backgroundColor: item.color,
                  }}
                />
              )}
              {indicator === 'line' && (
                <div
                  className={cn('h-0.5 w-2', indicatorClassName)}
                  style={{
                    backgroundColor: item.color,
                  }}
                />
              )}
              <span className="text-xs font-medium text-muted-foreground">
                {item.name}:
              </span>
              <span className="text-xs font-medium">{formattedValue}</span>
            </div>
          );
        })}
      </div>
    </div>
  );
}
