'use client';

import { Card } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import { cva } from 'class-variance-authority';
import { ChevronDown } from 'lucide-react';
// Light mode only
import { useCallback, useState } from 'react';
import {
  CartesianGrid,
  Legend,
  Line,
  LineChart as Re<PERSON>rtsLineChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from 'recharts';

const lineChartVariants = cva('', {
  variants: {
    variant: {
      default: '',
      outline: 'border rounded-lg p-4',
    },
    size: {
      default: 'h-[300px]',
      sm: 'h-[200px]',
      lg: 'h-[400px]',
      xl: 'h-[500px]',
    },
  },
  defaultVariants: {
    variant: 'default',
    size: 'default',
  },
});

export interface LineChartProps {
  data: Array<Record<string, unknown>>;
  series: Array<{
    name: string;
    dataKey: string;
    color?: string;
    strokeWidth?: number;
    type?: 'monotone' | 'linear' | 'step' | 'stepBefore' | 'stepAfter';
    dot?: boolean | object;
    activeDot?: boolean | object;
  }>;
  xAxisDataKey: string;
  yAxisLabel?: string;
  title?: string;
  className?: string;
  showLegend?: boolean;
  showGrid?: boolean;
  showTooltip?: boolean;
  variant?: 'default' | 'outline';
  size?: 'default' | 'sm' | 'lg' | 'xl';
  tooltipFormatter?: (value: unknown) => string;
  onPointClick?: (data: unknown, index: number) => void;
}

export function LineChart({
  data,
  series,
  xAxisDataKey,
  yAxisLabel,
  title,
  className,
  showLegend = true,
  showGrid = true,
  showTooltip = true,
  variant = 'default',
  size = 'default',
  tooltipFormatter,
  onPointClick,
}: LineChartProps) {
  // Always use light mode colors
  const textColor = '#424242';
  const gridColor = '#e0e0e0';

  const [activeIndex, setActiveIndex] = useState<number | null>(null);

  const handlePointClick = useCallback(
    (data: unknown, index: number) => {
      setActiveIndex(index === activeIndex ? null : index);
      if (onPointClick) {
        onPointClick(data, index);
      }
    },
    [activeIndex, onPointClick],
  );

  const renderContent = () => (
    <ResponsiveContainer width="100%" height="100%">
      <RechartsLineChart
        data={data}
        margin={{ top: 10, right: 30, left: 20, bottom: 40 }}
      >
        {showGrid && (
          <CartesianGrid
            strokeDasharray="3 3"
            vertical={false}
            stroke={gridColor}
          />
        )}
        <XAxis
          dataKey={xAxisDataKey}
          stroke={textColor}
          tick={{ fill: textColor, fontSize: 12 }}
          tickLine={{ stroke: textColor }}
          axisLine={{ stroke: textColor }}
        />
        <YAxis
          stroke={textColor}
          tick={{ fill: textColor, fontSize: 12 }}
          tickLine={{ stroke: textColor }}
          axisLine={{ stroke: textColor }}
          label={
            yAxisLabel
              ? {
                  value: yAxisLabel,
                  angle: -90,
                  position: 'insideLeft',
                  style: { textAnchor: 'middle', fill: textColor },
                }
              : undefined
          }
        />
        {showTooltip && (
          <Tooltip
            formatter={tooltipFormatter}
            contentStyle={{
              backgroundColor: '#fff',
              border: '1px solid #e0e0e0',
              borderRadius: '6px',
              color: textColor,
            }}
          />
        )}
        {showLegend && (
          <Legend
            verticalAlign="top"
            height={36}
            wrapperStyle={{ fontSize: '12px', color: textColor }}
          />
        )}
        {series.map((s, index) => (
          <Line
            key={`line-${index}`}
            type={s.type || 'monotone'}
            dataKey={s.dataKey}
            name={s.name}
            stroke={s.color || `hsl(${index * 40}, 70%, 60%)`}
            strokeWidth={s.strokeWidth || 2}
            dot={
              s.dot === false
                ? false
                : s.dot || {
                    r: 4,
                    strokeWidth: 1,
                    fill: 'white',
                    stroke: s.color || `hsl(${index * 40}, 70%, 60%)`,
                  }
            }
            activeDot={
              s.activeDot === false
                ? false
                : s.activeDot || {
                    r: 6,
                    onClick: handlePointClick,
                    style: { cursor: 'pointer' },
                  }
            }
            isAnimationActive={true}
            animationDuration={1000}
          />
        ))}
      </RechartsLineChart>
    </ResponsiveContainer>
  );

  if (variant === 'outline' || title) {
    return (
      <Card className={cn('overflow-hidden', className)}>
        {title && (
          <div className="flex justify-between items-center px-4 py-3 border-b">
            <div className="font-medium">{title}</div>
            <button className="rounded-full p-1 hover:bg-secondary">
              <ChevronDown className="h-4 w-4" />
            </button>
          </div>
        )}
        <div className={lineChartVariants({ variant: 'default', size })}>
          {renderContent()}
        </div>
      </Card>
    );
  }

  return (
    <div className={cn(lineChartVariants({ variant, size }), className)}>
      {renderContent()}
    </div>
  );
}
