'use client';

import { Card } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import { cva } from 'class-variance-authority';
import { ChevronDown } from 'lucide-react';
// Light mode only
import { useCallback, useState } from 'react';
import {
  Cell,
  Legend,
  Pie,
  Pie<PERSON><PERSON> as Recharts<PERSON>ie<PERSON>hart,
  ResponsiveContainer,
  Sector,
  Tooltip,
} from 'recharts';

const donutChartVariants = cva('', {
  variants: {
    variant: {
      default: '',
      outline: 'border rounded-lg p-4',
    },
    size: {
      default: 'h-[300px]',
      sm: 'h-[200px]',
      lg: 'h-[400px]',
      xl: 'h-[500px]',
    },
  },
  defaultVariants: {
    variant: 'default',
    size: 'default',
  },
});

export interface DonutChartProps {
  data: Array<{
    name: string;
    value: number;
    color?: string;
  }>;
  title?: string;
  className?: string;
  showLegend?: boolean;
  showTooltip?: boolean;
  variant?: 'default' | 'outline';
  size?: 'default' | 'sm' | 'lg' | 'xl';
  innerRadius?: number;
  outerRadius?: number;
  tooltipFormatter?: (value: unknown) => string;
  valueFormatterAction?: (value: number) => string;
  onSliceClick?: (data: unknown, index: number) => void;
  centerLabel?: string | React.ReactNode;
}

export function DonutChart({
  data,
  title,
  className,
  showLegend = true,
  showTooltip = true,
  variant = 'default',
  size = 'default',
  innerRadius = 60,
  outerRadius = 80,
  tooltipFormatter,
  valueFormatterAction = (value) => value.toString(),
  onSliceClick,
  centerLabel,
}: DonutChartProps) {
  // Always use light mode colors
  const textColor = '#424242';

  const [activeIndex, setActiveIndex] = useState<number>(-1);

  const handleSliceClick = useCallback(
    (_: unknown, index: number) => {
      setActiveIndex(index === activeIndex ? -1 : index);
      if (onSliceClick && data[index]) {
        onSliceClick(data[index], index);
      }
    },
    [activeIndex, data, onSliceClick],
  );

  // Define a proper type for the active shape props
  interface ActiveShapeProps {
    cx: number;
    cy: number;
    innerRadius: number;
    outerRadius: number;
    startAngle: number;
    endAngle: number;
    fill: string;
    payload: {
      name: string;
      value: number;
      [key: string]: unknown;
    };
    percent: number;
    value: number;
  }

  const renderActiveShape = (props: ActiveShapeProps) => {
    const {
      cx,
      cy,
      innerRadius,
      outerRadius,
      startAngle,
      endAngle,
      fill,
      payload,
      percent,
      value,
    } = props;

    return (
      <g>
        <text
          x={cx}
          y={cy - 4}
          dy={8}
          textAnchor="middle"
          fill={textColor}
          className="text-sm font-medium"
        >
          {payload.name}
        </text>
        <text
          x={cx}
          y={cy + 16}
          dy={8}
          textAnchor="middle"
          fill={textColor}
          className="text-xs"
        >
          {valueFormatterAction(value)} ({(percent * 100).toFixed(0)}%)
        </text>
        <Sector
          cx={cx}
          cy={cy}
          innerRadius={innerRadius}
          outerRadius={outerRadius + 8}
          startAngle={startAngle}
          endAngle={endAngle}
          fill={fill}
          className="drop-shadow-md"
        />
        <Sector
          cx={cx}
          cy={cy}
          startAngle={startAngle}
          endAngle={endAngle}
          innerRadius={innerRadius - 4}
          outerRadius={innerRadius - 1}
          fill={fill}
        />
      </g>
    );
  };

  // Center label renderer
  const renderCenterLabel = () => {
    if (!centerLabel) return null;

    return (
      <text
        x="50%"
        y="50%"
        textAnchor="middle"
        dominantBaseline="middle"
        className="fill-foreground text-sm font-medium"
      >
        {centerLabel}
      </text>
    );
  };

  const renderContent = () => (
    <ResponsiveContainer width="100%" height="100%">
      <RechartsPieChart>
        {showTooltip && (
          <Tooltip
            formatter={tooltipFormatter}
            contentStyle={{
              backgroundColor: '#fff',
              border: '1px solid #e0e0e0',
              borderRadius: '6px',
              color: textColor,
            }}
          />
        )}
        {showLegend && (
          <Legend
            layout="horizontal"
            verticalAlign="bottom"
            align="center"
            wrapperStyle={{
              fontSize: '12px',
              color: textColor,
              marginTop: '8px',
            }}
          />
        )}
        <Pie
          data={data}
          cx="50%"
          cy="50%"
          innerRadius={innerRadius}
          outerRadius={outerRadius}
          paddingAngle={2}
          dataKey="value"
          activeIndex={activeIndex}
          activeShape={
            renderActiveShape as (props: unknown) => React.ReactElement
          }
          onClick={handleSliceClick}
          isAnimationActive={true}
          animationDuration={800}
          className="cursor-pointer"
        >
          {data.map((entry, index) => (
            <Cell
              key={`cell-${index}`}
              fill={entry.color || `hsl(${index * 40}, 70%, 60%)`}
              strokeWidth={1}
              stroke="#ffffff"
            />
          ))}
        </Pie>
        {renderCenterLabel()}
      </RechartsPieChart>
    </ResponsiveContainer>
  );

  if (variant === 'outline' || title) {
    return (
      <Card className={cn('overflow-hidden', className)}>
        {title && (
          <div className="flex justify-between items-center px-4 py-3 border-b">
            <div className="font-medium">{title}</div>
            <button className="rounded-full p-1 hover:bg-secondary">
              <ChevronDown className="h-4 w-4" />
            </button>
          </div>
        )}
        <div className={donutChartVariants({ variant: 'default', size })}>
          {renderContent()}
        </div>
      </Card>
    );
  }

  return (
    <div className={cn(donutChartVariants({ variant, size }), className)}>
      {renderContent()}
    </div>
  );
}
