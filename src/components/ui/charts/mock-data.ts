// Mock data for dashboard charts

// PMA Certificates mock data
export function getPMACertsMockData() {
  return {
    // Status distribution
    statusDistribution: [
      { name: 'Valid', value: 42, color: '#4CAF50' },
      { name: 'Expired', value: 8, color: '#F44336' },
      { name: 'Expiring Soon', value: 12, color: '#FF9800' },
    ],

    // Monthly issuance trend
    monthlyTrend: [
      { month: 'Jan', issued: 5, renewed: 3 },
      { month: 'Feb', issued: 7, renewed: 2 },
      { month: 'Mar', issued: 4, renewed: 5 },
      { month: 'Apr', issued: 6, renewed: 4 },
      { month: 'May', issued: 8, renewed: 3 },
      { month: 'Jun', issued: 10, renewed: 6 },
    ],

    // By category
    categories: [
      { name: 'Electrical', count: 15 },
      { name: 'Mechanical', count: 18 },
      { name: 'Plumbing', count: 12 },
      { name: 'Fire Safety', count: 8 },
      { name: 'Structural', count: 9 },
    ],

    // Recent PMAs
    recent: [
      {
        id: 'PMA-2023-001',
        name: 'Annual Elevator Inspection',
        status: 'Valid',
        expiryDate: '2024-12-15',
      },
      {
        id: 'PMA-2023-002',
        name: 'Fire Safety System',
        status: 'Valid',
        expiryDate: '2024-10-22',
      },
      {
        id: 'PMA-2023-003',
        name: 'HVAC Maintenance Cert',
        status: 'Expiring Soon',
        expiryDate: '2024-08-05',
      },
      {
        id: 'PMA-2023-004',
        name: 'Electrical Systems Compliance',
        status: 'Valid',
        expiryDate: '2025-01-18',
      },
      {
        id: 'PMA-2023-005',
        name: 'Plumbing System Inspection',
        status: 'Expired',
        expiryDate: '2024-05-30',
      },
    ],
  };
}

// Maintenance Logs mock data
export function getMaintenanceLogsMockData() {
  return {
    // Status distribution
    statusDistribution: [
      { name: 'Completed', value: 67, color: '#4CAF50' },
      { name: 'Pending', value: 23, color: '#FF9800' },
      { name: 'Overdue', value: 10, color: '#F44336' },
    ],

    // Maintenance by type
    byType: [
      { name: 'Preventive', count: 45 },
      { name: 'Corrective', count: 32 },
      { name: 'Emergency', count: 5 },
      { name: 'Inspection', count: 18 },
    ],

    // Monthly trend
    monthlyTrend: [
      { month: 'Jan', preventive: 8, corrective: 5, emergency: 1 },
      { month: 'Feb', preventive: 7, corrective: 6, emergency: 0 },
      { month: 'Mar', preventive: 9, corrective: 4, emergency: 1 },
      { month: 'Apr', preventive: 6, corrective: 8, emergency: 0 },
      { month: 'May', preventive: 8, corrective: 5, emergency: 2 },
      { month: 'Jun', preventive: 7, corrective: 4, emergency: 1 },
    ],

    // Recent logs
    recent: [
      {
        id: 'ML-2024-045',
        title: 'HVAC Filter Replacement',
        type: 'Preventive',
        status: 'Completed',
        date: '2024-06-25',
      },
      {
        id: 'ML-2024-044',
        title: 'Elevator Annual Inspection',
        type: 'Inspection',
        status: 'Completed',
        date: '2024-06-22',
      },
      {
        id: 'ML-2024-043',
        title: 'Roof Leak Repair',
        type: 'Corrective',
        status: 'Pending',
        date: '2024-06-20',
      },
      {
        id: 'ML-2024-042',
        title: 'Parking Gate Malfunction',
        type: 'Emergency',
        status: 'Completed',
        date: '2024-06-18',
      },
      {
        id: 'ML-2024-041',
        title: 'Plumbing System Check',
        type: 'Preventive',
        status: 'Overdue',
        date: '2024-06-15',
      },
    ],
  };
}

// Complaints mock data
export function getComplaintsMockData() {
  return {
    // Status distribution
    statusDistribution: [
      { name: 'New', value: 12, color: '#2196F3' },
      { name: 'In Progress', value: 28, color: '#FF9800' },
      { name: 'Resolved', value: 45, color: '#4CAF50' },
      { name: 'Closed', value: 15, color: '#9E9E9E' },
    ],

    // By category
    byCategory: [
      { name: 'Facilities', count: 32 },
      { name: 'Services', count: 24 },
      { name: 'Safety', count: 18 },
      { name: 'Cleanliness', count: 15 },
      { name: 'Other', count: 11 },
    ],

    // Monthly trend
    monthlyTrend: [
      { month: 'Jan', received: 18, resolved: 15 },
      { month: 'Feb', received: 15, resolved: 13 },
      { month: 'Mar', received: 22, resolved: 18 },
      { month: 'Apr', received: 16, resolved: 14 },
      { month: 'May', received: 20, resolved: 17 },
      { month: 'Jun', received: 14, resolved: 13 },
    ],

    // Enhanced area chart data for complaints (past 12 months)
    areaChartData: [
      { month: 'January', total: 84 },
      { month: 'February', total: 100 },
      { month: 'March', total: 104 },
      { month: 'April', total: 133 },
      { month: 'May', total: 134 },
      { month: 'June', total: 143 },
      { month: 'July', total: 158 },
      { month: 'August', total: 149 },
      { month: 'September', total: 137 },
      { month: 'October', total: 124 },
      { month: 'November', total: 111 },
      { month: 'December', total: 100 },
    ],

    // Weekly trend data for detailed view
    weeklyTrend: [
      { week: 'Week 1', total: 34 },
      { week: 'Week 2', total: 43 },
      { week: 'Week 3', total: 30 },
      { week: 'Week 4', total: 42 },
    ],

    // Response time (in days)
    responseTime: [
      { category: 'Facilities', avgTime: 2.4 },
      { category: 'Services', avgTime: 1.8 },
      { category: 'Safety', avgTime: 1.2 },
      { category: 'Cleanliness', avgTime: 2.1 },
      { category: 'Other', avgTime: 3.0 },
    ],

    // Recent complaints
    recent: [
      {
        id: 'C-2024-098',
        title: 'AC not working in meeting room',
        category: 'Facilities',
        status: 'In Progress',
        date: '2024-06-26',
      },
      {
        id: 'C-2024-097',
        title: 'Water leakage in bathroom',
        category: 'Facilities',
        status: 'New',
        date: '2024-06-25',
      },
      {
        id: 'C-2024-096',
        title: 'Poor cleaning in common area',
        category: 'Cleanliness',
        status: 'Resolved',
        date: '2024-06-23',
      },
      {
        id: 'C-2024-095',
        title: 'Broken light in parking area',
        category: 'Safety',
        status: 'Resolved',
        date: '2024-06-22',
      },
      {
        id: 'C-2024-094',
        title: 'Elevator service disruption',
        category: 'Services',
        status: 'Closed',
        date: '2024-06-20',
      },
    ],
  };
}
