'use client';

import { Sheet, Sheet<PERSON>ontent, SheetTrigger } from '@/components/ui/sheet';
import { cn } from '@/lib/utils';
import { BarChart3, Home, Menu, User } from 'lucide-react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';

const tabs = [
    { name: 'Dashboard', href: '/dashboard', icon: Home },
    { name: 'Projects', href: '/projects', icon: BarChart3 },
    { name: 'Profile', href: '/profile', icon: User },
];

export function MobileBottomNav() {
    const pathname = usePathname();

    return (
        <nav className="fixed bottom-0 inset-x-0 z-50 bg-background border-t shadow-md">
            <div className="flex justify-between items-center h-16 px-4">
                {tabs.map((tab) => {
                    const isActive = pathname === tab.href;
                    return (
                        <Link
                            key={tab.name}
                            href={tab.href}
                            className={cn(
                                'flex flex-col items-center justify-center space-y-1 text-sm',
                                isActive ? 'text-primary' : 'text-muted-foreground hover:text-foreground',
                            )}
                        >
                            <tab.icon className="h-5 w-5" />
                            <span>{tab.name}</span>
                        </Link>
                    );
                })}
                <Sheet>
                    <SheetTrigger asChild>
                        <button
                            aria-label="Open Menu"
                            className="flex flex-col items-center justify-center space-y-1 text-sm text-muted-foreground hover:text-foreground"
                        >
                            <Menu className="h-5 w-5" />
                            <span>Menu</span>
                        </button>
                    </SheetTrigger>
                    <SheetContent side="left">
                        <div className="p-4">
                            <h2 className="text-lg font-semibold">Menu</h2>
                            <ul className="mt-4 space-y-2">
                                <li>
                                    <Link href="/maintenance-logs" className="text-sm text-muted-foreground hover:text-foreground">
                                        Maintenance Logs
                                    </Link>
                                </li>
                                <li>
                                    <Link href="/complaints" className="text-sm text-muted-foreground hover:text-foreground">
                                        Complaints
                                    </Link>
                                </li>
                                <li>
                                    <Link href="/members" className="text-sm text-muted-foreground hover:text-foreground">
                                        Members
                                    </Link>
                                </li>
                            </ul>
                        </div>
                    </SheetContent>
                </Sheet>
            </div>
        </nav>
    );
}
