'use client';

import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import { usePermissions } from '@/hooks/use-permissions';
import { useNavigationTranslations } from '@/hooks/use-translations';
import { Link, usePathname } from '@/i18n/navigation';
import { cn } from '@/lib/utils';
import { useProjectContext } from '@/providers/project-context';
import { useQueryClient } from '@tanstack/react-query';
import {
    ArrowLeft,
    BarChart3,
    Calendar,
    Home,
    Menu,
    MessageSquare,
    Shield,
    User,
    UserCheck,
    Users as UsersIcon
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useCallback, useMemo } from 'react';

// Menu item interface
interface MenuItem {
    title: string;
    url: string;
    icon: React.ComponentType<{ className?: string }>;
    translationKey: string;
}

// Menu item constants (same as unified sidebar)
const PROJECT_MENU_ITEMS: MenuItem[] = [
    {
        title: 'Dashboard',
        url: '/dashboard',
        icon: Home,
        translationKey: 'dashboard',
    },
    {
        title: 'PMAs',
        url: '/pmas',
        icon: Shield,
        translationKey: 'pmas',
    },
    {
        title: 'Maintenance Logs',
        url: '/maintenance-logs',
        icon: Calendar,
        translationKey: 'maintenanceLogs',
    },
    {
        title: 'Complaints',
        url: '/complaints',
        icon: MessageSquare,
        translationKey: 'complaints',
    },
    {
        title: 'Members',
        url: '/members',
        icon: UsersIcon,
        translationKey: 'members',
    },
];

const GENERAL_MENU_ITEMS: MenuItem[] = [
    {
        title: 'Projects',
        url: '/projects',
        icon: BarChart3,
        translationKey: 'projects',
    },
    {
        title: 'Profile',
        url: '/profile',
        icon: User,
        translationKey: 'profile',
    },
];

const CONTRACTOR_MENU_ITEMS: MenuItem[] = [
    ...GENERAL_MENU_ITEMS,
    {
        title: 'CP List',
        url: '/cp-list',
        icon: UserCheck,
        translationKey: 'cpList',
    },
];

// Utility functions
const getCleanPathname = (pathname: string): string => {
    return pathname.replace(/^\/(en|ms)/, '') || '/';
};

const isRouteActive = (url: string, pathname: string): boolean => {
    const cleanPathname = getCleanPathname(pathname);

    if (url === '/projects') {
        return cleanPathname === '/projects' || cleanPathname === '/';
    }
    if (url === '/dashboard') {
        return cleanPathname === '/dashboard';
    }
    return cleanPathname.startsWith(url);
};

export function MobileBottomNav() {
    const pathname = usePathname();
    const router = useRouter();
    const { isLoading, userRole } = usePermissions();
    const { selectedProjectId, clearProject, setSelectedProject } = useProjectContext();
    const t = useNavigationTranslations();
    const queryClient = useQueryClient();

    // Memoized values
    const isInProjectContext = useMemo(
        () => Boolean(selectedProjectId),
        [selectedProjectId],
    );

    const currentMenuItems = useMemo(() => {
        if (isInProjectContext) {
            return PROJECT_MENU_ITEMS;
        }

        // Role-based menu items for general context
        switch (userRole) {
            case 'contractor':
                return CONTRACTOR_MENU_ITEMS;
            case 'admin':
            case 'viewer':
            default:
                return GENERAL_MENU_ITEMS;
        }
    }, [isInProjectContext, userRole]);

    // Get the first 3 items for main navigation, rest go to overflow menu
    const mainNavItems = currentMenuItems.slice(0, 3);
    const overflowItems = currentMenuItems.slice(3);

    // Memoized callback for back to projects
    const handleBackToProjects = useCallback(() => {
        try {
            // Set a flag to indicate user intentionally cleared project
            sessionStorage.setItem('user_cleared_project', 'true');
            // Immediately update query data to trigger re-render
            queryClient.setQueryData(['selectedProject'], null);
            // Clear project context
            clearProject();
            setSelectedProject(null);
            // Navigate after clearing context
            router.replace('/projects');
        } catch (error) {
            console.error('Failed to navigate back to projects:', error);
        }
    }, [clearProject, setSelectedProject, router, queryClient]);

    if (isLoading) {
        return (
            <nav className="fixed bottom-0 inset-x-0 z-50 bg-background border-t shadow-md">
                <div className="flex justify-between items-center h-16 px-4">
                    {[...Array(4)].map((_, index) => (
                        <div key={index} className="flex flex-col items-center justify-center space-y-1">
                            <div className="h-5 w-5 bg-muted animate-pulse rounded" />
                            <div className="h-3 w-8 bg-muted animate-pulse rounded" />
                        </div>
                    ))}
                </div>
            </nav>
        );
    }

    return (
        <nav className="fixed bottom-0 inset-x-0 z-50 bg-background border-t shadow-md">
            <div className="flex justify-between items-center h-16 px-4">
                {mainNavItems.map((item) => {
                    const isActive = isRouteActive(item.url, pathname);
                    const IconComponent = item.icon;
                    return (
                        <Link
                            key={item.translationKey}
                            href={item.url}
                            className={cn(
                                'flex flex-col items-center justify-center space-y-1 text-xs',
                                isActive ? 'text-primary' : 'text-muted-foreground hover:text-foreground',
                            )}
                        >
                            <IconComponent className="h-5 w-5" />
                            <span className="truncate max-w-[60px]">{t(item.translationKey)}</span>
                        </Link>
                    );
                })}

                {/* Menu button for overflow items or back to projects */}
                <Sheet>
                    <SheetTrigger asChild>
                        <button
                            aria-label="Open Menu"
                            className="flex flex-col items-center justify-center space-y-1 text-xs text-muted-foreground hover:text-foreground"
                        >
                            <Menu className="h-5 w-5" />
                            <span>Menu</span>
                        </button>
                    </SheetTrigger>
                    <SheetContent side="left" className="w-80">
                        <div className="p-4">
                            <h2 className="text-lg font-semibold mb-4">
                                {isInProjectContext ? 'Project Menu' : 'Menu'}
                            </h2>

                            {/* Back to Projects button when in project context */}
                            {isInProjectContext && (
                                <div className="mb-4 pb-4 border-b">
                                    <button
                                        onClick={handleBackToProjects}
                                        className="flex items-center gap-2 text-sm text-muted-foreground hover:text-foreground transition-colors w-full p-2 rounded-md hover:bg-muted"
                                    >
                                        <ArrowLeft className="h-4 w-4" />
                                        Back to Projects
                                    </button>
                                </div>
                            )}

                            {/* Overflow menu items */}
                            {overflowItems.length > 0 && (
                                <ul className="space-y-2">
                                    {overflowItems.map((item) => {
                                        const IconComponent = item.icon;
                                        const isActive = isRouteActive(item.url, pathname);
                                        return (
                                            <li key={item.translationKey}>
                                                <Link
                                                    href={item.url}
                                                    className={cn(
                                                        "flex items-center gap-3 text-sm p-2 rounded-md transition-colors",
                                                        isActive
                                                            ? 'text-primary bg-primary/10'
                                                            : 'text-muted-foreground hover:text-foreground hover:bg-muted'
                                                    )}
                                                >
                                                    <IconComponent className="h-4 w-4" />
                                                    {t(item.translationKey)}
                                                </Link>
                                            </li>
                                        );
                                    })}
                                </ul>
                            )}
                        </div>
                    </SheetContent>
                </Sheet>
            </div>
        </nav>
    );
}
