'use client';

import { SidebarInset } from '@/components/ui/sidebar';
import { useIsMobile } from '@/hooks/use-mobile';
import { MobileBottomNav } from './mobile-bottom-nav';
import { AppSidebar } from './sidebar';

export function MobileNavWrapper({ children }: { children: React.ReactNode }) {
    const isMobile = useIsMobile();

    return (
        <>
            {isMobile ? (
                <>
                    <MobileBottomNav />
                    <main className="pb-16">{children}</main>
                </>
            ) : (
                <>
                    <AppSidebar />
                    <SidebarInset>
                        {children}
                    </SidebarInset>
                </>
            )}
        </>
    );
}
