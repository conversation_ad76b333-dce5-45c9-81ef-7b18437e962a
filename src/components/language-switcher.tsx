'use client';

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
} from '@/components/ui/select';
import { useNavigationTranslations } from '@/hooks/use-translations';
import { usePathname, useRouter } from '@/i18n/navigation';
import { Globe } from 'lucide-react';
import { useLocale } from 'next-intl';
import { useEffect, useState } from 'react';

const languages = ['en', 'ms'] as const;

export function LanguageSwitcher() {
  const locale = useLocale();
  const pathname = usePathname();
  const router = useRouter();
  const t = useNavigationTranslations();
  const [currentLocale, setCurrentLocale] = useState(locale);

  // Update current locale when the locale changes
  useEffect(() => {
    setCurrentLocale(locale);
  }, [locale]);

  const handleLanguageChange = (newLocale: string) => {
    // Update the current locale state immediately for UI feedback
    setCurrentLocale(newLocale as (typeof languages)[number]);

    // Use the pathname directly as it's already locale-aware
    router.replace(pathname, { locale: newLocale });
  };

  const getCurrentLanguageName = () => {
    return t(
      `languages.${currentLocale}` as `languages.${typeof currentLocale}`,
    );
  };

  return (
    <Select value={currentLocale} onValueChange={handleLanguageChange}>
      <SelectTrigger className="w-full h-8 text-sm">
        <div className="flex items-center gap-2">
          <Globe className="h-3.5 w-3.5 shrink-0" />
          <span className="truncate">{getCurrentLanguageName()}</span>
        </div>
      </SelectTrigger>
      <SelectContent>
        {languages.map((code) => (
          <SelectItem key={code} value={code} className="text-sm">
            {t(`languages.${code}` as `languages.${typeof code}`)}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}
