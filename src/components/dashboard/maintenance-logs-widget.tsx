'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { DonutChart } from '@/components/ui/charts';
import { useMaintenanceLogsStats } from '@/features/dashboard/hooks';
import { cn } from '@/lib/utils';
import {
  Activity,
  ArrowRight,
  BarChart3,
  CheckCircle,
  HelpCircle,
  Timer,
  XCircle,
} from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useRouter } from 'next/navigation';

export function MaintenanceLogsWidget() {
  const { data: maintenanceStats, isLoading } = useMaintenanceLogsStats();
  const router = useRouter();
  const t = useTranslations('dashboard.widgets.maintenance');

  // Calculate total maintenance logs from status distribution
  const totalMaintenanceLogs = maintenanceStats?.statusDistribution
    ? maintenanceStats.statusDistribution.reduce(
        (sum, item) => sum + item.value,
        0,
      )
    : 0;

  // Ensure we have at least some data to display in the chart
  const hasMaintenanceData = totalMaintenanceLogs > 0;

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold">{t('title')}</h2>
        <Button
          variant="ghost"
          size="sm"
          className="gap-1 hover:bg-blue-50 transition-colors"
          onClick={() => router.push('/maintenance-logs')}
        >
          {t('viewAll')} <ArrowRight className="h-4 w-4" />
        </Button>
      </div>

      {/* Charts section */}
      <div className="grid gap-4 md:grid-cols-2">
        {/* Enhanced Maintenance Status Chart */}
        <Card className="overflow-hidden border border-gray-100 rounded-xl shadow-md hover:shadow-lg transition-all duration-300 bg-gradient-to-br from-blue-50/80 to-indigo-50/50">
          <CardHeader className="pb-2 border-b border-gray-100">
            <CardTitle className="text-base font-medium flex items-center gap-2 text-blue-800">
              <Activity className="h-4 w-4 text-blue-600" />
              {t('maintenanceStatus')}
            </CardTitle>
          </CardHeader>
          <CardContent className="p-5">
            {isLoading ? (
              <div className="flex items-center justify-center h-[200px]">
                <div className="flex flex-col items-center">
                  <div className="animate-spin w-8 h-8 border-3 border-blue-200 border-t-blue-600 rounded-full mb-2"></div>
                  <p className="text-sm text-muted-foreground">
                    {t('loading')}
                  </p>
                </div>
              </div>
            ) : !maintenanceStats || totalMaintenanceLogs === 0 ? (
              <div className="flex flex-col items-center justify-center py-10 px-6 text-center space-y-4 h-[200px]">
                <div className="relative">
                  <BarChart3 className="h-16 w-16 text-gray-200" />
                  <HelpCircle className="h-6 w-6 text-blue-500 absolute bottom-0 right-0" />
                </div>
                <div>
                  <h3 className="text-sm font-medium mb-1">
                    {t('noMaintenanceData')}
                  </h3>
                  <p className="text-xs text-gray-500 mb-3">{t('noLogs')}</p>
                  <Button
                    variant="outline"
                    size="sm"
                    className="border-blue-200 text-blue-600 hover:bg-blue-50"
                    onClick={() => router.push('/maintenance-logs/create')}
                  >
                    {t('addMaintenanceLog')}
                  </Button>
                </div>
              </div>
            ) : (
              <div className="flex flex-col space-y-4">
                {/* Chart with Total Count in Center */}
                <div className="relative">
                  <DonutChart
                    data={
                      maintenanceStats?.statusDistribution
                        ? maintenanceStats.statusDistribution.map((item) => ({
                            name: item.name,
                            value: item.value,
                            color: item.color,
                          }))
                        : [{ name: 'No Data', value: 1, color: '#e0e0e0' }]
                    }
                    size="sm"
                    innerRadius={60}
                    outerRadius={78}
                    centerLabel={
                      <g>
                        <text
                          x="50%"
                          y="50%"
                          textAnchor="middle"
                          dominantBaseline="middle"
                          className="fill-gray-400 text-xs font-normal"
                          dy="-10"
                        >
                          Total
                        </text>
                        <text
                          x="50%"
                          y="50%"
                          textAnchor="middle"
                          dominantBaseline="middle"
                          className="fill-gray-900 text-xl font-bold"
                          dy="12"
                        >
                          {hasMaintenanceData ? totalMaintenanceLogs : '0'}
                        </text>
                      </g>
                    }
                    showLegend={false}
                    showTooltip={true}
                    variant="default"
                  />
                </div>

                {/* Modern stat cards below chart */}
                <div
                  className="grid grid-cols-3 gap-3 mt-1"
                  style={{ minHeight: '80px' }}
                >
                  <div className="flex flex-col bg-white rounded-lg p-3 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300 cursor-pointer">
                    <div className="flex justify-between items-center mb-1">
                      <div className="w-2 h-2 rounded-full bg-emerald-300"></div>
                      <CheckCircle className="h-3.5 w-3.5 text-emerald-600" />
                    </div>
                    <span className="text-xs font-medium text-gray-500">
                      Fully Function
                    </span>
                    <span className="text-lg font-bold text-gray-800 mt-0.5">
                      {maintenanceStats?.statusDistribution?.find(
                        (item) => item.name === 'Fully Function',
                      )?.value || 0}
                    </span>
                    {(maintenanceStats?.statusDistribution?.find(
                      (item) => item.name === 'Fully Function',
                    )?.value || 0) > 0 ? (
                      <div className="flex items-center gap-1 mt-1">
                        <div
                          className={cn(
                            'h-1 bg-emerald-100 rounded-full flex-grow',
                            totalMaintenanceLogs > 0 &&
                              'relative overflow-hidden',
                          )}
                        >
                          {totalMaintenanceLogs > 0 && (
                            <div
                              className="absolute inset-y-0 bg-emerald-300 rounded-full"
                              style={{
                                width: `${((maintenanceStats?.statusDistribution?.find((item) => item.name === 'Fully Function')?.value || 0) / totalMaintenanceLogs) * 100}%`,
                              }}
                            ></div>
                          )}
                        </div>
                      </div>
                    ) : null}
                  </div>

                  <div className="flex flex-col bg-white rounded-lg p-3 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300 cursor-pointer">
                    <div className="flex justify-between items-center mb-1">
                      <div className="w-2 h-2 rounded-full bg-amber-300"></div>
                      <Timer className="h-3.5 w-3.5 text-amber-600" />
                    </div>
                    <span className="text-xs font-medium text-gray-500">
                      Partially Function
                    </span>
                    <span className="text-lg font-bold text-gray-800 mt-0.5">
                      {maintenanceStats?.statusDistribution?.find(
                        (item) => item.name === 'Partially Function',
                      )?.value || 0}
                    </span>
                    {(maintenanceStats?.statusDistribution?.find(
                      (item) => item.name === 'Partially Function',
                    )?.value || 0) > 0 ? (
                      <div className="flex items-center gap-1 mt-1">
                        <div
                          className={cn(
                            'h-1 bg-amber-100 rounded-full flex-grow',
                            totalMaintenanceLogs > 0 &&
                              'relative overflow-hidden',
                          )}
                        >
                          {totalMaintenanceLogs > 0 && (
                            <div
                              className="absolute inset-y-0 bg-amber-300 rounded-full"
                              style={{
                                width: `${((maintenanceStats?.statusDistribution?.find((item) => item.name === 'Partially Function')?.value || 0) / totalMaintenanceLogs) * 100}%`,
                              }}
                            ></div>
                          )}
                        </div>
                      </div>
                    ) : null}
                  </div>

                  <div className="flex flex-col bg-white rounded-lg p-3 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300 cursor-pointer">
                    <div className="flex justify-between items-center mb-1">
                      <div className="w-2 h-2 rounded-full bg-red-300"></div>
                      <XCircle className="h-3.5 w-3.5 text-red-600" />
                    </div>
                    <span className="text-xs font-medium text-gray-500">
                      Broken
                    </span>
                    <span className="text-lg font-bold text-gray-800 mt-0.5">
                      {maintenanceStats?.statusDistribution?.find(
                        (item) => item.name === 'Broken',
                      )?.value || 0}
                    </span>
                    {(maintenanceStats?.statusDistribution?.find(
                      (item) => item.name === 'Broken',
                    )?.value || 0) > 0 ? (
                      <div className="flex items-center gap-1 mt-1">
                        <div
                          className={cn(
                            'h-1 bg-red-100 rounded-full flex-grow',
                            totalMaintenanceLogs > 0 &&
                              'relative overflow-hidden',
                          )}
                        >
                          {totalMaintenanceLogs > 0 && (
                            <div
                              className="absolute inset-y-0 bg-red-300 rounded-full"
                              style={{
                                width: `${((maintenanceStats?.statusDistribution?.find((item) => item.name === 'Broken')?.value || 0) / totalMaintenanceLogs) * 100}%`,
                              }}
                            ></div>
                          )}
                        </div>
                      </div>
                    ) : null}
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Status Summary Section */}
        <Card className="overflow-hidden border border-gray-100 rounded-xl shadow-md hover:shadow-lg transition-all duration-300 bg-gradient-to-br from-gray-50/80 to-white">
          <CardHeader className="pb-2 border-b border-gray-100">
            <CardTitle className="text-base font-medium flex items-center gap-2 text-slate-800">
              <Activity className="h-4 w-4 text-blue-600" />
              {t('maintenanceStatus')}
            </CardTitle>
          </CardHeader>
          <CardContent className="p-5">
            {isLoading ? (
              <div className="h-40 flex items-center justify-center">
                <div className="flex flex-col items-center">
                  <div className="animate-spin w-8 h-8 border-3 border-blue-200 border-t-blue-600 rounded-full mb-2"></div>
                  <p className="text-sm text-muted-foreground">
                    {t('loading')}
                  </p>
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                {maintenanceStats?.statusDistribution?.map((status) => (
                  <div
                    key={status.name}
                    className="flex items-center justify-between"
                  >
                    <div className="flex items-center gap-2">
                      <div
                        className="w-3 h-3 rounded-full"
                        style={{ backgroundColor: status.color }}
                      />
                      <span className="text-sm font-medium">{status.name}</span>
                    </div>
                    <span className="text-lg font-bold">{status.value}</span>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
