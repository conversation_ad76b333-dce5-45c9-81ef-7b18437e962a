'use client';

import {} from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { usePMACertificatesStats } from '@/features/dashboard/hooks';
import { cn, getCurrency } from '@/lib/utils';
import {
  AlertCircle,
  ArrowRight,
  BarChart3,
  CheckCircle,
  Clock,
  DollarSign,
  // FileCheck,
  PieChart,
  Shield,
  Timer,
  TrendingDown,
  XCircle,
} from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useRouter } from 'next/navigation';
import { DonutChart } from '../ui/charts/donut-chart';

export function PMACertificatesWidget() {
  const { data: pmaStats, isLoading } = usePMACertificatesStats();
  const router = useRouter();
  const t = useTranslations('dashboard.widgets.pma');

  // Get problematic lifts data from PMACertificatesStats
  const problematicLifts = pmaStats?.problematicLifts || [];

  // Calculate total certificates
  const totalCertificates =
    (pmaStats?.validCount || 0) +
    (pmaStats?.expiredCount || 0) +
    (pmaStats?.expiringSoonCount || 0);

  // Define pastel colors for the donut chart
  const chartColors = {
    active: '#A7F3D0', // pastel emerald
    expiring: '#FEF3C7', // pastel amber
    expired: '#FECACA', // pastel red
  };

  // Prepare chart data
  const certificateData = [
    {
      name: t('active'),
      value: pmaStats?.validCount || 0,
      color: chartColors.active,
    },
    {
      name: t('expiringSoon'),
      value: pmaStats?.expiringSoonCount || 0,
      color: chartColors.expiring,
    },
    {
      name: t('expired'),
      value: pmaStats?.expiredCount || 0,
      color: chartColors.expired,
    },
  ];

  // Check if all certificate data is zero
  const hasNoCertificateData = certificateData.every(
    (item) => item.value === 0,
  );

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold">{t('title')}</h2>
        <Button
          variant="ghost"
          size="sm"
          className="gap-1 hover:bg-blue-50 transition-colors"
          onClick={() => router.push('/pmas')}
        >
          {t('viewAll')} <ArrowRight className="h-4 w-4" />
        </Button>
      </div>

      {isLoading ? (
        <div className="h-60 flex items-center justify-center">
          <div className="flex flex-col items-center">
            <div className="animate-spin w-10 h-10 border-4 border-blue-200 border-t-blue-600 rounded-full mb-3"></div>
            <p className="text-muted-foreground">{t('loading')}</p>
          </div>
        </div>
      ) : (
        <>
          <div className="grid gap-4 md:grid-cols-2">
            {/* Modern certificate status chart with improved visuals */}
            <Card className="overflow-hidden border border-gray-100 rounded-xl shadow-md hover:shadow-lg transition-all duration-300 bg-gradient-to-br from-blue-50/80 to-indigo-50/50">
              <CardHeader className="pb-2 border-b border-gray-100">
                <CardTitle className="text-base font-medium flex items-center gap-2 text-blue-800">
                  <Shield className="h-4 w-4 text-blue-600" />
                  {t('certificateStatus')}
                </CardTitle>
              </CardHeader>
              <CardContent className="p-5">
                {hasNoCertificateData ? (
                  // Empty state for certificate chart
                  <div className="flex flex-col items-center justify-center py-10 px-6 text-center space-y-4">
                    <div className="relative">
                      <PieChart className="h-16 w-16 text-gray-200" />
                      <AlertCircle className="h-6 w-6 text-blue-500 absolute bottom-0 right-0" />
                    </div>
                    <div>
                      <h3 className="text-sm font-medium mb-1">
                        {t('noCertificateData')}
                      </h3>
                      <p className="text-xs text-gray-500 mb-3">
                        {t('noCertificateMessage')}
                      </p>
                      <Button
                        variant="outline"
                        size="sm"
                        className="border-blue-200 text-blue-600 hover:bg-blue-50"
                        onClick={() => router.push('/pmas/add')}
                      >
                        {t('addCertificate')}
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div className="flex flex-col space-y-4">
                    {/* Chart with Total Count in Center */}
                    <div className="relative">
                      <DonutChart
                        data={certificateData}
                        size="sm"
                        innerRadius={60}
                        outerRadius={78}
                        centerLabel={
                          <g>
                            <text
                              x="50%"
                              y="50%"
                              textAnchor="middle"
                              dominantBaseline="middle"
                              className="fill-gray-400 text-xs font-normal"
                              dy="-10"
                            >
                              Total
                            </text>
                            <text
                              x="50%"
                              y="50%"
                              textAnchor="middle"
                              dominantBaseline="middle"
                              className="fill-gray-900 text-xl font-bold"
                              dy="12"
                            >
                              {totalCertificates}
                            </text>
                          </g>
                        }
                        showLegend={false}
                        showTooltip={true}
                      />
                    </div>

                    {/* Modern stat cards below chart */}
                    <div className="grid grid-cols-3 gap-3 mt-1">
                      <div className="flex flex-col bg-white rounded-lg p-3 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300 cursor-pointer">
                        <div className="flex justify-between items-center mb-1">
                          <div className="w-2 h-2 rounded-full bg-emerald-300"></div>
                          <CheckCircle className="h-3.5 w-3.5 text-emerald-600" />
                        </div>
                        <span className="text-xs font-medium text-gray-500">
                          Active
                        </span>
                        <span className="text-lg font-bold text-gray-800 mt-0.5">
                          {pmaStats?.validCount || 0}
                        </span>
                        {pmaStats?.validCount ? (
                          <div className="flex items-center gap-1 mt-1">
                            <div
                              className={cn(
                                'h-1 bg-emerald-100 rounded-full flex-grow',
                                totalCertificates > 0 &&
                                  'relative overflow-hidden',
                              )}
                            >
                              {totalCertificates > 0 && (
                                <div
                                  className="absolute inset-y-0 bg-emerald-300 rounded-full"
                                  style={{
                                    width: `${(pmaStats.validCount / totalCertificates) * 100}%`,
                                  }}
                                ></div>
                              )}
                            </div>
                          </div>
                        ) : null}
                      </div>

                      <div className="flex flex-col bg-white rounded-lg p-3 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300 cursor-pointer">
                        <div className="flex justify-between items-center mb-1">
                          <div className="w-2 h-2 rounded-full bg-amber-300"></div>
                          <Timer className="h-3.5 w-3.5 text-amber-600" />
                        </div>
                        <span className="text-xs font-medium text-gray-500">
                          Expiring
                        </span>
                        <span className="text-lg font-bold text-gray-800 mt-0.5">
                          {pmaStats?.expiringSoonCount || 0}
                        </span>
                        {pmaStats?.expiringSoonCount ? (
                          <div className="flex items-center gap-1 mt-1">
                            <div
                              className={cn(
                                'h-1 bg-amber-100 rounded-full flex-grow',
                                totalCertificates > 0 &&
                                  'relative overflow-hidden',
                              )}
                            >
                              {totalCertificates > 0 && (
                                <div
                                  className="absolute inset-y-0 bg-amber-300 rounded-full"
                                  style={{
                                    width: `${(pmaStats.expiringSoonCount / totalCertificates) * 100}%`,
                                  }}
                                ></div>
                              )}
                            </div>
                          </div>
                        ) : null}
                      </div>

                      <div className="flex flex-col bg-white rounded-lg p-3 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300 cursor-pointer">
                        <div className="flex justify-between items-center mb-1">
                          <div className="w-2 h-2 rounded-full bg-red-300"></div>
                          <XCircle className="h-3.5 w-3.5 text-red-600" />
                        </div>
                        <span className="text-xs font-medium text-gray-500">
                          Expired
                        </span>
                        <span className="text-lg font-bold text-gray-800 mt-0.5">
                          {pmaStats?.expiredCount || 0}
                        </span>
                        {pmaStats?.expiredCount ? (
                          <div className="flex items-center gap-1 mt-1">
                            <div
                              className={cn(
                                'h-1 bg-red-100 rounded-full flex-grow',
                                totalCertificates > 0 &&
                                  'relative overflow-hidden',
                              )}
                            >
                              {totalCertificates > 0 && (
                                <div
                                  className="absolute inset-y-0 bg-red-300 rounded-full"
                                  style={{
                                    width: `${(pmaStats.expiredCount / totalCertificates) * 100}%`,
                                  }}
                                ></div>
                              )}
                            </div>
                          </div>
                        ) : null}
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Enhanced Problematic Lifts Section */}
            <Card className="overflow-hidden border border-gray-100 rounded-xl shadow-md hover:shadow-lg transition-all duration-300 bg-gradient-to-br from-slate-50/80 to-slate-100/30">
              <CardHeader className="pb-2 border-b border-gray-100">
                <CardTitle className="text-base font-medium flex items-center gap-2 text-slate-800">
                  <TrendingDown className="h-4 w-4 text-red-500" />
                  {t('mostProblematicLifts')}
                </CardTitle>
              </CardHeader>
              <CardContent className="p-5">
                {problematicLifts.length > 0 ? (
                  <div className="space-y-4">
                    {problematicLifts.map((lift, index) => (
                      <div
                        key={index}
                        className="bg-white rounded-lg p-3 border border-gray-100 shadow-sm hover:shadow-md transition-all duration-300 space-y-3 cursor-pointer group"
                      >
                        <div className="flex justify-between items-center">
                          <div className="flex items-center gap-2">
                            <span
                              className={cn(
                                'flex items-center justify-center w-7 h-7 rounded-full text-xs font-medium',
                                index === 0
                                  ? 'bg-red-100 text-red-700 group-hover:bg-red-200 transition-colors'
                                  : index === 1
                                    ? 'bg-amber-100 text-amber-700 group-hover:bg-amber-200 transition-colors'
                                    : 'bg-blue-100 text-blue-700 group-hover:bg-blue-200 transition-colors',
                              )}
                            >
                              {index + 1}
                            </span>
                            <span className="font-medium truncate max-w-[150px] text-gray-800">
                              {lift.pmaNumber}
                            </span>
                          </div>
                          <div>
                            <span
                              className={cn(
                                'text-xs px-2.5 py-1 rounded-full font-medium',
                                index === 0
                                  ? 'bg-red-50 text-red-600 border border-red-200'
                                  : index === 1
                                    ? 'bg-amber-50 text-amber-600 border border-amber-200'
                                    : 'bg-blue-50 text-blue-600 border border-blue-200',
                              )}
                            >
                              {lift.incidents} incidents
                            </span>
                          </div>
                        </div>

                        <div className="grid grid-cols-2 gap-3">
                          <div className="flex items-center gap-2 bg-slate-50 rounded-lg p-2">
                            <div
                              className={cn(
                                'p-1.5 rounded-md',
                                index === 0
                                  ? 'bg-red-100'
                                  : index === 1
                                    ? 'bg-amber-100'
                                    : 'bg-blue-100',
                              )}
                            >
                              <DollarSign
                                className={cn(
                                  'h-3.5 w-3.5',
                                  index === 0
                                    ? 'text-red-600'
                                    : index === 1
                                      ? 'text-amber-600'
                                      : 'text-blue-600',
                                )}
                              />
                            </div>
                            <div className="flex flex-col">
                              <span className="text-xs text-gray-500">
                                Cost
                              </span>
                              <span className="text-sm font-semibold">
                                {getCurrency(lift.cost ?? 0)}
                              </span>
                            </div>
                          </div>
                          <div className="flex items-center gap-2 bg-slate-50 rounded-lg p-2">
                            <div
                              className={cn(
                                'p-1.5 rounded-md',
                                index === 0
                                  ? 'bg-red-100'
                                  : index === 1
                                    ? 'bg-amber-100'
                                    : 'bg-blue-100',
                              )}
                            >
                              <Clock
                                className={cn(
                                  'h-3.5 w-3.5',
                                  index === 0
                                    ? 'text-red-600'
                                    : index === 1
                                      ? 'text-amber-600'
                                      : 'text-blue-600',
                                )}
                              />
                            </div>
                            <div className="flex flex-col">
                              <span className="text-xs text-gray-500">
                                Downtime
                              </span>
                              <span className="text-sm font-semibold">
                                {lift.hours}h
                              </span>
                            </div>
                          </div>
                        </div>

                        <Progress
                          value={
                            problematicLifts.length > 0
                              ? (lift.incidents /
                                  Math.max(
                                    ...problematicLifts.map((l) => l.incidents),
                                  )) *
                                100
                              : 0
                          }
                          className={cn(
                            'h-1.5 rounded-full',
                            index === 0
                              ? 'bg-red-100'
                              : index === 1
                                ? 'bg-amber-100'
                                : 'bg-blue-100',
                          )}
                        />
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="flex flex-col items-center justify-center py-10 px-6 text-center space-y-4">
                    <div className="relative">
                      <BarChart3 className="h-16 w-16 text-gray-200 animate-pulse" />
                      <CheckCircle className="h-6 w-6 text-emerald-500 absolute bottom-0 right-0" />
                    </div>
                    <div>
                      <h3 className="text-sm font-medium mb-1">
                        {t('noProblematicLifts')}
                      </h3>
                      <p className="text-xs text-gray-500">
                        {t('allLiftsNormal')}
                      </p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </>
      )}
    </div>
  );
}
