'use client';

import { clearProjectIdFromCookies } from '@/lib/middleware-utils';
import { useProjectContext } from '@/providers/project-context';
import { useQueryClient } from '@tanstack/react-query';
import { ChevronRight, Home } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useCallback } from 'react';

interface ProjectHeaderProps {
  projectName?: string;
  module: string;
}

export function ProjectHeader({ projectName, module }: ProjectHeaderProps) {
  const router = useRouter();
  const queryClient = useQueryClient();
  const { clearProject, setSelectedProject } = useProjectContext();

  // Handle clearing project when clicking "Projects"
  const handleBackToProjects = useCallback(() => {
    try {
      // Immediately clear cookies to prevent refetch
      clearProjectIdFromCookies();
      // Set a flag to indicate user intentionally cleared project
      sessionStorage.setItem('user_cleared_project', 'true');
      // Immediately update query data to trigger re-render
      queryClient.setQueryData(['selectedProject'], null);
      // Clear project context
      clearProject();
      setSelectedProject(null);
      // Navigate after clearing context
      router.replace('/projects');
    } catch (error) {
      console.error('Failed to navigate back to projects:', error);
    }
  }, [clearProject, setSelectedProject, router, queryClient]);

  return (
    <header>
      {/* Minimalist Navigation Bar */}
      <div className="border-b border-gray-200 bg-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            {/* Simple Breadcrumbs */}
            <nav
              className="flex items-center space-x-2 text-sm"
              aria-label="Breadcrumb"
            >
              <Link
                href="/dashboard"
                className="text-gray-500 hover:text-gray-700"
              >
                <Home className="h-4 w-4" />
              </Link>
              <ChevronRight className="h-4 w-4 text-gray-300" />
              <button
                onClick={handleBackToProjects}
                className="text-gray-600 hover:text-gray-900"
              >
                Projects
              </button>
              {projectName && (
                <>
                  <ChevronRight className="h-4 w-4 text-gray-300" />
                  <span className="text-gray-600 max-w-[200px] truncate">
                    {projectName}
                  </span>
                </>
              )}
              <ChevronRight className="h-4 w-4 text-gray-300" />
              {/* <span className="text-gray-900 font-medium">{module}</span> */}
            </nav>
          </div>
        </div>
      </div>
    </header>
  );
}
