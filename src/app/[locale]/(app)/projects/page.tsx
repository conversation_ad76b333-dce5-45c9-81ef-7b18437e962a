'use client';

import {
  PendingInvitations,
  ProjectList,
  ProjectStatsCards,
  useProjects,
  useProjectStats,
} from '@/features/projects';
import { CreateProjectButton } from '@/features/projects/components/create-project-button';
import { FolderOpen } from 'lucide-react';
import { useTranslations } from 'next-intl';

const ProjectsPage = () => {
  const t = useTranslations('pages.projects');

  const { data: projects = [], isLoading: projectsLoading } = useProjects();
  const { data: stats, isLoading: statsLoading } = useProjectStats();

  return (
    <div className="container mx-auto py-4 sm:py-6 px-4 sm:px-6">
      <div className="space-y-4 sm:space-y-6 md:space-y-8">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3 sm:gap-4">
          <div className="flex items-center space-x-3 sm:space-x-4">
            <div className="p-2 sm:p-3 rounded-xl bg-primary/10">
              <FolderOpen className="h-6 w-6 sm:h-8 sm:w-8 text-primary" />
            </div>
            <div>
              <h1 className="text-2xl sm:text-3xl font-bold tracking-tight text-gray-900">
                {t('title')}
              </h1>
              <p className="text-sm sm:text-base text-gray-500 mt-1">{t('description')}</p>
            </div>
          </div>
          <div className="w-full sm:w-auto">
            <CreateProjectButton />
          </div>
        </div>

        {/* Stats Cards */}
        {stats && <ProjectStatsCards stats={stats} isLoading={statsLoading} />}

        {/* Pending Invitations */}
        <PendingInvitations />

        {/* Projects List */}
        <div className="space-y-4 sm:space-y-6">
          <div className="flex items-center justify-between">
            <h2 className="text-lg sm:text-xl font-semibold text-gray-900">
              Recent Projects
            </h2>
          </div>

          <ProjectList projects={projects} isLoading={projectsLoading} />
        </div>
      </div>
    </div>
  );
};

export default ProjectsPage;
