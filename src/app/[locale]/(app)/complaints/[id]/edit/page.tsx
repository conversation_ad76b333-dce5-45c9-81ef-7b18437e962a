'use client';

import { RepairInformationForm } from '@/features/complaints/components/repair-information-form';
import { useComplaints } from '@/features/complaints/hooks/use-complaints-simple';
import { useParams, useRouter } from 'next/navigation';

export default function EditComplaintPage() {
  const router = useRouter();
  const params = useParams();
  const complaintId = params.id as string;

  // Fetch the complaint data to populate the form
  const { data: complaintsData = [], isLoading } = useComplaints();
  const complaint = complaintsData.find((c) => c.id === complaintId);

  const handleSuccess = () => {
    // Redirect back to complaints list with success message
    router.push('/complaints?success=updated');
  };

  const handleCancel = () => {
    // Navigate to complaints dashboard
    router.push('/complaints');
  };

  // Show loading state while fetching data
  if (isLoading) {
    return (
      <div className="container mx-auto p-6 space-y-6">
        <div className="flex items-center justify-center py-8">
          <div className="flex items-center gap-2">
            <div className="h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent" />
            Loading complaint data...
          </div>
        </div>
      </div>
    );
  }

  // Show error if complaint not found
  if (!complaint) {
    return (
      <div className="container mx-auto p-6 space-y-6">
        <div className="flex flex-col items-center gap-2 py-8">
          <p className="text-red-600">Complaint not found</p>
          <button
            onClick={() => router.push('/complaints')}
            className="text-blue-600 hover:underline"
          >
            Back to complaints
          </button>
        </div>
      </div>
    );
  }

  // Prepare initial data from the complaint - using correct field names for RepairInformationForm
  const initialData = {
    // Section A: Basic Information
    email: complaint.email,
    date: new Date(complaint.date),
    expected_completion_date: new Date(
      complaint.expected_completion_date || '',
    ),
    contractor_name: complaint.contractor_name || '',
    location: complaint.location || '',
    no_pma_lif: complaint.no_pma_lif || '',
    description: complaint.description || '',
    involves_mantrap: complaint.involves_mantrap || false,

    // Section B: Repair Information - preserve existing data when editing
    actual_completion_date: complaint.actual_completion_date
      ? new Date(complaint.actual_completion_date)
      : undefined,
    repair_completion_time: complaint.repair_completion_time || '',
    cause_of_damage: complaint.cause_of_damage || '',
    correction_action: complaint.correction_action || '',
    repair_cost: complaint.repair_cost || 0,
    proof_of_repair_urls: complaint.proof_of_repair_urls || [],

    // Status
    status: complaint.status,
  };

  return (
    <RepairInformationForm
      onSuccess={handleSuccess}
      onCancel={handleCancel}
      complaintId={complaintId}
      initialData={initialData}
    />
  );
}
