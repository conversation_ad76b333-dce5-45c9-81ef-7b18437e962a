'use client';

import { MobileNavWrapper } from '@/components/mobile-nav-wrapper';
import { ProjectHeader } from '@/components/project-header';
import { SidebarProvider } from '@/components/ui/sidebar';
import { getCurrentModuleFromPathname } from '@/lib/utils';
import { useProjectContext } from '@/providers/project-context';
import { usePathname } from 'next/navigation';
import React from 'react';

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { selectedProject } = useProjectContext();

  const pathname = usePathname();

  const currentModule: string = React.useMemo(
    () => getCurrentModuleFromPathname(pathname),
    [pathname],
  );

  return (
    <SidebarProvider>
      <MobileNavWrapper>
        <main className="flex-1 overflow-auto">
          {selectedProject && (
            <div className="hidden md:block">
              <ProjectHeader
                projectName={selectedProject.name}
                module={currentModule}
              />
            </div>
          )}
          <div className="flex-1 space-y-4 p-4 md:p-8 pt-6 w-full max-w-none">{children}</div>
        </main>
      </MobileNavWrapper>
    </SidebarProvider>
  );
}
