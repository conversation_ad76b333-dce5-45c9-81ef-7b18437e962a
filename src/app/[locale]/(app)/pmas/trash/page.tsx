'use client';

import { Button } from '@/components/ui/button';
import { SoftDeletedPMAManager } from '@/features/pma-management';
import { useProjectContext } from '@/providers/project-context';
import { ArrowLeft, Trash2 } from 'lucide-react';
import Link from 'next/link';
import { useParams } from 'next/navigation';

export default function PMATrashPage() {
  const params = useParams();
  const locale = (params?.locale as string) || 'en';
  const { selectedProjectId } = useProjectContext();

  if (!selectedProjectId) {
    return (
      <div className="min-h-screen bg-gray-50/30 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-xl font-semibold text-gray-900 mb-2">
            No Project Selected
          </h1>
          <p className="text-gray-600">
            Please select a project to view deleted PMA certificates.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50/30">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Link href={`/${locale}/pmas`}>
              <Button variant="ghost" size="sm" className="p-2">
                <ArrowLeft className="h-4 w-4" />
              </Button>
            </Link>
            <div>
              <h1 className="text-xl font-semibold text-gray-900 flex items-center gap-2">
                <Trash2 className="h-5 w-5" />
                PMA Certificates Trash
              </h1>
              <p className="text-sm text-gray-600 mt-1">
                Manage deleted PMA certificates
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        <div className="max-w-4xl mx-auto">
          <SoftDeletedPMAManager projectId={selectedProjectId} />
        </div>
      </div>
    </div>
  );
}
