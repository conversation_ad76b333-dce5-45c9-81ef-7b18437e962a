import { supabase } from '@/lib/supabase';
import type {
  Competent<PERSON>erson,
  CompetentPersonInsert,
  CompetentPersonUpdate,
  CPType,
} from '@/types/competent-person';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

/**
 * Hook to get all competent persons for a specific contractor
 * @param contractorId - The contractor ID to fetch competent persons for
 */
export function useCompetentPersons(contractorId?: string) {
  return useQuery({
    queryKey: ['competent-persons', contractorId],
    queryFn: async () => {
      if (!contractorId) throw new Error('Contractor ID is required');

      const { data, error } = await supabase
        .from('competent_person')
        .select('*')
        .eq('contractor_id', contractorId)
        .is('deleted_at', null) // Only get non-deleted competent persons
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data as unknown as Competent<PERSON>erson[];
    },
    enabled: !!contractorId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Hook to get a single competent person by ID
 * @param competentPersonId - The competent person ID
 */
export function useCompetentPerson(competentPersonId?: string) {
  return useQuery({
    queryKey: ['competent-person', competentPersonId],
    queryFn: async () => {
      if (!competentPersonId)
        throw new Error('Competent person ID is required');

      const { data, error } = await supabase
        .from('competent_person')
        .select('*')
        .eq('id', competentPersonId)
        .is('deleted_at', null)
        .single();

      if (error) throw error;
      return data as unknown as CompetentPerson;
    },
    enabled: !!competentPersonId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Hook to create a new competent person
 */
export function useCreateCompetentPerson() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: ['competent-person', 'create'],
    mutationFn: async (competentPersonData: CompetentPersonInsert) => {
      const { data, error } = await supabase
        .from('competent_person')
        .insert(competentPersonData)
        .select()
        .single();

      if (error) throw error;
      return data as unknown as CompetentPerson;
    },
    onSuccess: (data) => {
      // Invalidate all competent person queries for this contractor
      queryClient.invalidateQueries({
        queryKey: ['competent-persons'],
        predicate: (query) => {
          const queryKey = query.queryKey as string[];
          return queryKey.includes(data.contractor_id);
        },
      });

      // Update individual competent person cache
      queryClient.setQueryData(['competent-person', data.id], data);
    },
  });
}

/**
 * Hook to update a competent person
 */
export function useUpdateCompetentPerson() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: ['competent-person', 'update'],
    mutationFn: async ({
      competentPersonId,
      updates,
    }: {
      competentPersonId: string;
      updates: Partial<CompetentPersonUpdate>;
    }) => {
      const { data, error } = await supabase
        .from('competent_person')
        .update({
          ...updates,
          updated_at: new Date().toISOString(),
        })
        .eq('id', competentPersonId)
        .select()
        .single();

      if (error) throw error;
      return data as unknown as CompetentPerson;
    },
    onSuccess: (data) => {
      // Invalidate all competent person queries for this contractor
      queryClient.invalidateQueries({
        queryKey: ['competent-persons'],
        predicate: (query) => {
          const queryKey = query.queryKey as string[];
          return queryKey.includes(data.contractor_id);
        },
      });

      // Update individual competent person cache
      queryClient.setQueryData(['competent-person', data.id], data);
    },
  });
}

/**
 * Hook to soft delete a competent person
 */
export function useDeleteCompetentPerson() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: ['competent-person', 'delete'],
    mutationFn: async (competentPersonId: string) => {
      const { data, error } = await supabase
        .from('competent_person')
        .update({
          deleted_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        })
        .eq('id', competentPersonId)
        .select()
        .single();

      if (error) throw error;
      return data as unknown as CompetentPerson;
    },
    onSuccess: (data) => {
      // Invalidate all competent person queries for this contractor
      queryClient.invalidateQueries({
        queryKey: ['competent-persons'],
        predicate: (query) => {
          const queryKey = query.queryKey as string[];
          return queryKey.includes(data.contractor_id);
        },
      });

      // Remove individual competent person cache
      queryClient.removeQueries({ queryKey: ['competent-person', data.id] });
    },
  });
}

/**
 * Hook to get competent persons by CP type
 * @param contractorId - The contractor ID
 * @param cpType - The CP type to filter by
 */
export function useCompetentPersonsByType(
  contractorId?: string,
  cpType?: CPType,
) {
  return useQuery({
    queryKey: ['competent-persons', 'by-type', contractorId, cpType],
    queryFn: async () => {
      if (!contractorId || !cpType) {
        throw new Error('Contractor ID and CP type are required');
      }

      const { data, error } = await supabase
        .from('competent_person')
        .select('*')
        .eq('contractor_id', contractorId)
        .eq('cp_type', cpType)
        .is('deleted_at', null)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data as unknown as CompetentPerson[];
    },
    enabled: !!contractorId && !!cpType,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}
