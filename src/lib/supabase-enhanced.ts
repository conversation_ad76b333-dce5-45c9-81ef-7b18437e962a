import type { Database } from '@/types/database';
import { createBrowserClient } from '@supabase/ssr';

/**
 * Enhanced Supabase client with PKCE authentication support
 * Configured for proper authentication flows in production
 */
function createEnhancedSupabaseClient() {
  const client = createBrowserClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      auth: {
        flowType: 'pkce',
        autoRefreshToken: true,
        persistSession: true,
        detectSessionInUrl: true,
        // Use consistent storage key across all clients
        storageKey: `sb-${new URL(process.env.NEXT_PUBLIC_SUPABASE_URL!).hostname}-auth-token`,
      },
      global: {
        headers: {
          'X-Client-Info': 'simple-fe@1.0.0',
        },
      },
    },
  );

  return client;
}

// Create and export the enhanced client
export const supabase = createEnhancedSupabaseClient();
