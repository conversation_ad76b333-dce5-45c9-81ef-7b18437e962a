import { useForceProfileRefresh } from '@/hooks/use-force-profile-refresh';
import { supabase } from '@/lib/supabase';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import type { AdminOnboardingFormValues } from '../schemas/admin-onboarding-schemas';

/**
 * Hook to complete admin onboarding process
 * Updates admin profile with access mode and monitoring state
 */
export function useCompleteAdminOnboarding() {
  const queryClient = useQueryClient();
  const router = useRouter();
  const forceProfileRefresh = useForceProfileRefresh();

  return useMutation({
    mutationKey: ['admin-onboarding', 'complete'],
    mutationFn: async (values: AdminOnboardingFormValues) => {
      // Get current user
      const {
        data: { user },
        error: userError,
      } = await supabase.auth.getUser();

      if (userError || !user) {
        throw new Error('User not authenticated');
      }

      // Prepare update data
      const updateData = {
        name: values.fullName,
        phone_number: values.phoneNumber || null,
        admin_access_mode: values.adminAccessMode,
        monitoring_state:
          values.adminAccessMode === 'state'
            ? values.monitoringState || null
            : null,
        onboarding_completed: true,
        updated_at: new Date().toISOString(),
      };

      // Update user profile with admin configuration
      const { error: profileError } = await supabase
        .from('users')
        .update(updateData)
        .eq('id', user.id);

      if (profileError) {
        throw new Error(`Profile update failed: ${profileError.message}`);
      }

      return {
        adminAccessMode: values.adminAccessMode,
        monitoringState: values.monitoringState,
      };
    },
    onSuccess: async (result) => {
      try {
        // Clear onboarding and user role cookies to force refresh (only on client)
        if (typeof document !== 'undefined') {
          document.cookie =
            'onboarding_completed=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;';
          document.cookie =
            'user_role=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;';
        }

        // Show success message
        const accessModeText =
          result.adminAccessMode === 'project'
            ? 'project-level access'
            : `state-level access for ${result.monitoringState}`;

        toast.success(
          `Admin setup completed successfully! You now have ${accessModeText}.`,
        );

        // Invalidate all relevant queries and wait for completion
        await Promise.all([
          queryClient.invalidateQueries({ queryKey: ['user-with-profile'] }),
          queryClient.invalidateQueries({ queryKey: ['profile'] }),
          queryClient.invalidateQueries({ queryKey: ['permissions'] }),
          queryClient.invalidateQueries({ queryKey: ['user'] }),
          queryClient.invalidateQueries({ queryKey: ['session'] }),
        ]);

        // Also clear the entire query cache to be extra sure
        queryClient.clear();

        // Use dedicated force refresh function for comprehensive cache clearing
        await forceProfileRefresh();

        // Wait a bit longer to ensure cache is properly updated
        await new Promise((resolve) => setTimeout(resolve, 1000));

        // Navigate to dashboard after successful onboarding completion
        router.push('/dashboard');
      } catch {
        // Still navigate to dashboard even if cache cleanup fails
        router.push('/dashboard');
      }
    },
    onError: (error: Error) => {
      toast.error(
        error.message || 'Failed to complete admin setup. Please try again.',
      );
    },
  });
}
