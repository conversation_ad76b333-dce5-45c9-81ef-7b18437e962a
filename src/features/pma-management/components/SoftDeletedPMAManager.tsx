'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  <PERSON><PERSON>Title,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Alert<PERSON>riangle, RotateCcw, Trash2 } from 'lucide-react';
import { useState } from 'react';
import {
  usePermanentlyDeletePMACertificate,
  useRestorePMACertificate,
  useSoftDeletedPMACertificates,
} from '../hooks/use-pma-certificates';
import type { PMACertificate } from '../types/pma-certificate';

interface SoftDeletedPMAManagerProps {
  projectId: string;
}

export function SoftDeletedPMAManager({
  projectId,
}: SoftDeletedPMAManagerProps) {
  const [selectedCertificate, setSelectedCertificate] =
    useState<PMACertificate | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  const { data: softDeletedCertificates, isLoading } =
    useSoftDeletedPMACertificates(projectId);
  const restoreMutation = useRestorePMACertificate();
  const permanentDeleteMutation = usePermanentlyDeletePMACertificate();

  const handleRestore = async (certificate: PMACertificate) => {
    try {
      await restoreMutation.mutateAsync(certificate.id);
    } catch (error) {
      console.error('Failed to restore certificate:', error);
    }
  };

  const handlePermanentDelete = async (certificate: PMACertificate) => {
    try {
      await permanentDeleteMutation.mutateAsync(certificate.id);
      setIsDeleteDialogOpen(false);
      setSelectedCertificate(null);
    } catch (error) {
      console.error('Failed to permanently delete certificate:', error);
    }
  };

  if (isLoading) {
    return (
      <Card className="p-6">
        <div className="flex items-center justify-center">
          <div className="animate-spin h-6 w-6 border-2 border-primary border-t-transparent rounded-full" />
          <span className="ml-2">Loading deleted certificates...</span>
        </div>
      </Card>
    );
  }

  if (!softDeletedCertificates || softDeletedCertificates.length === 0) {
    return (
      <Card className="p-6">
        <div className="text-center text-gray-500">
          <Trash2 className="h-12 w-12 mx-auto mb-4 text-gray-300" />
          <p>No deleted PMA certificates found</p>
        </div>
      </Card>
    );
  }

  return (
    <Card className="p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">
            Deleted PMA Certificates
          </h3>
          <p className="text-sm text-gray-600">
            {softDeletedCertificates.length} certificate(s) in trash
          </p>
        </div>
      </div>

      <div className="space-y-4">
        {softDeletedCertificates.map((certificate) => (
          <div
            key={certificate.id}
            className="flex items-center justify-between p-4 border border-gray-200 rounded-lg bg-gray-50"
          >
            <div className="flex-1">
              <div className="flex items-center gap-3">
                <h4 className="font-medium text-gray-900">
                  {certificate.pma_number || 'No PMA Number'}
                </h4>
                <Badge variant="secondary" className="text-xs">
                  {certificate.status}
                </Badge>
              </div>
              <div className="mt-1 text-sm text-gray-600">
                <p>Location: {certificate.location || 'N/A'}</p>
                <p>Expiry Date: {certificate.expiry_date}</p>
                <p>
                  Deleted:{' '}
                  {new Date(certificate.deleted_at!).toLocaleDateString()}
                </p>
              </div>
            </div>

            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleRestore(certificate)}
                disabled={restoreMutation.isPending}
                className="gap-2"
              >
                <RotateCcw className="h-4 w-4" />
                Restore
              </Button>

              <Dialog
                open={
                  isDeleteDialogOpen &&
                  selectedCertificate?.id === certificate.id
                }
                onOpenChange={(open) => {
                  setIsDeleteDialogOpen(open);
                  if (!open) setSelectedCertificate(null);
                }}
              >
                <DialogTrigger asChild>
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={() => setSelectedCertificate(certificate)}
                    disabled={permanentDeleteMutation.isPending}
                    className="gap-2"
                  >
                    <Trash2 className="h-4 w-4" />
                    Delete Permanently
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle className="flex items-center gap-2">
                      <AlertTriangle className="h-5 w-5 text-destructive" />
                      Permanently Delete Certificate
                    </DialogTitle>
                    <DialogDescription>
                      This action cannot be undone. The certificate will be
                      permanently removed from the database.
                    </DialogDescription>
                  </DialogHeader>
                  <div className="py-4">
                    <p className="font-medium">
                      PMA Number: {certificate.pma_number || 'No PMA Number'}
                    </p>
                    <p className="text-sm text-gray-600">
                      Location: {certificate.location || 'N/A'}
                    </p>
                  </div>
                  <DialogFooter>
                    <Button
                      variant="outline"
                      onClick={() => {
                        setIsDeleteDialogOpen(false);
                        setSelectedCertificate(null);
                      }}
                    >
                      Cancel
                    </Button>
                    <Button
                      variant="destructive"
                      onClick={() => handlePermanentDelete(certificate)}
                      disabled={permanentDeleteMutation.isPending}
                    >
                      {permanentDeleteMutation.isPending
                        ? 'Deleting...'
                        : 'Delete Permanently'}
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </div>
          </div>
        ))}
      </div>
    </Card>
  );
}
