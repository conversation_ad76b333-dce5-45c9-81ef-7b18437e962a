import { StatusCard } from '@/components/status-card';
import { Clock, FileText, XCircle } from 'lucide-react';
import React from 'react';

interface StatusCardsProps {
  activeCount: number;
  expirySoonCount: number;
  expiredCount: number;
  tPma: (key: string) => string;
}

export const StatusCards: React.FC<StatusCardsProps> = ({
  activeCount,
  expirySoonCount,
  expiredCount,
  tPma,
}) => (
  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 sm:gap-6 lg:gap-8 mb-8 sm:mb-12">
    <StatusCard
      title={tPma('status.active')}
      value={activeCount}
      description={tPma('statistics.activePmas')}
      icon={<FileText className="h-6 w-6" />}
      variant="success"
      className="w-full"
    />
    <StatusCard
      title={tPma('status.expiring_soon')}
      value={expirySoonCount}
      description={tPma('statistics.expiringSoon')}
      icon={<Clock className="h-6 w-6" />}
      variant="warning"
      className="w-full"
    />
    <StatusCard
      title={tPma('status.expired')}
      value={expiredCount}
      description={tPma('statistics.overdue')}
      icon={<XCircle className="h-6 w-6" />}
      variant="default"
      className="w-full"
    />
  </div>
);
