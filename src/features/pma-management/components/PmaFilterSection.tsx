'use client';

import { FilterSection } from '@/components/filter-section';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ColumnVisibilityToggle } from '@/components/ui/ColumnVisibilityToggle';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { X } from 'lucide-react';
import { useTranslations } from 'next-intl';
import type { PmaDbStatus } from '../types/pma-certificate';

interface PmaFilterSectionProps {
  filters: { search: string; status?: PmaDbStatus | 'all' };
  onFilterChange: (filters: {
    search: string;
    status?: PmaDbStatus | 'all';
  }) => void;
  columns: { key: string; label: string }[];
  tableState: {
    columnVisibility: Record<string, boolean>;
  };
  onTableStateChange: (state: {
    columnVisibility: Record<string, boolean>;
  }) => void;
}

const presetViews = {
  essential: {
    label: 'Essential',
    description: 'Show only the most important columns',
    columns: ['pma_number', 'location', 'status', 'actions'],
  },
  detailed: {
    label: 'Detailed',
    description: 'Show all available columns',
    columns: [
      'pma_number',
      'location',
      'expiry_date',
      'status',
      'total_repair_cost',
      'total_repair_time',
      'actions',
    ],
  },
  compact: {
    label: 'Compact',
    description: 'Optimized for smaller screens',
    columns: ['pma_number', 'status', 'actions'],
  },
};

export function PmaFilterSection({
  filters,
  onFilterChange,
  columns,
  tableState,
  onTableStateChange,
}: PmaFilterSectionProps) {
  const t = useTranslations('pmaManagement');

  // Render the status filter for the popover
  const renderFilters = (
    <div className="space-y-3">
      <label className="text-sm font-medium text-gray-700">
        {t('table.status')}
      </label>
      <Select
        value={filters.status || 'all'}
        onValueChange={(value: PmaDbStatus | 'all') =>
          onFilterChange({
            ...filters,
            status: value === 'all' ? undefined : value,
          })
        }
      >
        <SelectTrigger className="w-full border-gray-300 focus:border-gray-400 rounded-lg">
          <SelectValue placeholder="Select status" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">All Status</SelectItem>
          <SelectItem value="valid">Active</SelectItem>
          <SelectItem value="validating">Expiring Soon</SelectItem>
          <SelectItem value="invalid">Expired</SelectItem>
        </SelectContent>
      </Select>
    </div>
  );

  // Render active filter badges
  const getActiveFilterBadges = (
    filters: { search: string; status?: PmaDbStatus | 'all' },
    onFilterChange: (filters: {
      search: string;
      status?: PmaDbStatus | 'all';
    }) => void,
  ) => {
    const badges = [];
    if (filters.status) {
      let label = '';
      if (filters.status === 'valid') label = 'Active';
      else if (filters.status === 'validating') label = 'Expiring Soon';
      else if (filters.status === 'invalid') label = 'Expired';
      else label = filters.status;
      badges.push(
        <Badge
          key="status"
          variant="secondary"
          className="h-7 text-xs bg-emerald-100 text-emerald-700 border border-emerald-200 rounded-lg"
        >
          Status: {label}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onFilterChange({ ...filters, status: undefined })}
            className="ml-1 h-auto p-0 hover:bg-transparent"
          >
            <X className="h-3 w-3" />
          </Button>
        </Badge>,
      );
    }
    return badges;
  };

  // Render column visibility toggle
  const columnVisibilityToggle = (
    <ColumnVisibilityToggle
      columns={columns}
      columnVisibility={tableState.columnVisibility}
      onColumnVisibilityChange={(columnVisibility) => {
        onTableStateChange({ columnVisibility });
      }}
      presetViews={presetViews}
    />
  );

  return (
    <FilterSection
      filters={filters}
      onFilterChange={onFilterChange}
      columns={columns}
      columnVisibility={tableState.columnVisibility}
      onColumnVisibilityChange={(newVisibility) =>
        onTableStateChange({ columnVisibility: newVisibility })
      }
      tableState={tableState}
      onTableStateChange={onTableStateChange}
      renderFilters={renderFilters}
      searchPlaceholder={t('filters.searchPlaceholder')}
      getActiveFilterBadges={getActiveFilterBadges}
      columnVisibilityToggle={columnVisibilityToggle}
    />
  );
}
