import { supabase } from '@/lib/supabase';
import type {
  CreatePMACertificateInput,
  CreatePMACertificateResponse,
  PMACertificateStatus,
} from '../types/create-pma';

export async function createPMACertificate(
  data: CreatePMACertificateInput,
): Promise<CreatePMACertificateResponse> {
  // Validate required fields before insertion
  if (!data.pma_number || data.pma_number.trim() === '') {
    throw new Error('PMA number is required and cannot be empty');
  }

  // Validate that competent person ID is provided if not null
  if (data.competent_person_id && data.competent_person_id.trim() !== '') {
    // Validate that the competent person exists in the competent_person table
    const { data: competentPerson, error: competentPersonError } =
      await supabase
        .from('competent_person')
        .select('id')
        .eq('id', data.competent_person_id)
        .single();

    if (competentPersonError || !competentPerson) {
      throw new Error(
        `Invalid competent person ID: ${data.competent_person_id}. The competent person does not exist in the system.`,
      );
    }
  }

  // Debug: Log input data
  console.log('Creating PMA certificate with data:', {
    pma_number: data.pma_number,
    project_id: data.project_id,
    status: data.status,
    competent_person_id: data.competent_person_id,
  });

  const { data: insertedPma, error } = await supabase
    .from('pma_certificates')
    .insert([
      {
        // Add explicit array for insert
        pma_number: data.pma_number,
        expiry_date: data.expiry_date,
        status: data.status,
        project_id: data.project_id,
        location: data.location,
        state: data.state,
        competent_person_id: data.competent_person_id,
        file_url: data.file_url,
      },
    ])
    .select('*') // Explicitly select all fields
    .single();

  if (error) {
    console.error('Error creating PMA certificate:', {
      error,
      errorMessage: error.message,
      details: error.details,
      hint: error.hint,
    });
    throw new Error(`Failed to create PMA certificate: ${error.message}`);
  }

  if (!insertedPma) {
    console.error('No data returned from insert operation');
    throw new Error('No PMA certificate data returned after insertion');
  }

  // Debug: Log inserted data
  console.log('PMA certificate created:', insertedPma);

  // Validate required fields
  if (!insertedPma.id) {
    throw new Error('Missing required field: id');
  }
  if (!insertedPma.pma_number || insertedPma.pma_number.trim() === '') {
    console.error('PMA data after insert:', insertedPma);
    throw new Error('PMA number is missing or empty in database response');
  }
  if (!insertedPma.expiry_date) {
    throw new Error('Missing required field: expiry_date');
  }
  if (!insertedPma.status) {
    throw new Error('Missing required field: status');
  }
  if (!insertedPma.project_id) {
    throw new Error('Missing required field: project_id');
  }
  if (!insertedPma.location) {
    throw new Error('Missing required field: location');
  }
  if (!insertedPma.created_at) {
    throw new Error('Missing required field: created_at');
  }

  // Transform and validate the response to match CreatePMACertificateResponse type
  const response: CreatePMACertificateResponse = {
    id: insertedPma.id!, // Non-null assertion since we validated earlier
    pma_number: insertedPma.pma_number!, // Non-null assertion since we validated earlier
    expiry_date: insertedPma.expiry_date!, // Non-null assertion since we validated earlier
    status: insertedPma.status! as PMACertificateStatus, // Non-null assertion since we validated earlier
    project_id: insertedPma.project_id!, // Non-null assertion since we validated earlier
    location: insertedPma.location!, // Non-null assertion since we validated earlier
    state: insertedPma.state,
    competent_person_id: insertedPma.competent_person_id,
    file_url: insertedPma.file_url,
    created_at: insertedPma.created_at!, // Non-null assertion since we validated earlier
  };

  return response;
}
