import { useUserWithProfile } from '@/hooks/use-auth';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import {
  deletePMACertificate,
  fetchPMACertificates,
  fetchPMACertificateStats,
  getPMACertificateById,
  getSoftDeletedPMACertificates,
  permanentlyDeletePMACertificate,
  restorePMACertificate,
  updatePMACertificate,
} from '../services/pma-certificate-service';
import type { CreatePMACertificateInput } from '../types/create-pma';
import type { PMACertificate, PmaDbStatus } from '../types/pma-certificate';

export const PMA_CERTIFICATES_QUERY_KEY = 'pma-certificates';
export const PMA_STATS_QUERY_KEY = 'pma-stats';

export const usePMACertificates = (
  projectId: string | null,
  page = 1,
  pageSize = 10,
  sorting?: { column: string; direction: 'asc' | 'desc' },
  searchQuery?: string,
  filters?: { status?: PmaDbStatus | 'all' },
) => {
  return useQuery<{ data: PMACertificate[]; total: number } | undefined, Error>(
    {
      queryKey: [
        PMA_CERTIFICATES_QUERY_KEY,
        projectId,
        page,
        pageSize,
        sorting,
        searchQuery,
        filters,
      ],
      queryFn: () =>
        fetchPMACertificates(
          projectId!,
          page,
          pageSize,
          sorting,
          searchQuery,
          filters,
        ),
      enabled: !!projectId,
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 10 * 60 * 1000, // 10 minutes
    },
  );
};

export function usePMAStats(projectId: string | null) {
  return useQuery({
    queryKey: [PMA_STATS_QUERY_KEY, projectId],
    queryFn: () => fetchPMACertificateStats(projectId!),
    enabled: !!projectId,
    staleTime: 15 * 60 * 1000, // 15 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
  });
}

export function usePMACertificate(id: string) {
  return useQuery({
    queryKey: ['pma-certificate', id],
    queryFn: () => getPMACertificateById(id),
    enabled: !!id,
  });
}

export function useUpdatePMACertificate() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: ({
      id,
      updates,
    }: {
      id: string;
      updates: Partial<CreatePMACertificateInput>;
    }) => updatePMACertificate(id, updates),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({
        queryKey: [PMA_CERTIFICATES_QUERY_KEY],
      });
      queryClient.invalidateQueries({ queryKey: ['pma-certificate', id] });
      // Also invalidate PMA stats to ensure counts are updated
      queryClient.invalidateQueries({
        queryKey: [PMA_STATS_QUERY_KEY],
      });
    },
  });
}

export function useDeletePMACertificate() {
  const queryClient = useQueryClient();
  const { data: user } = useUserWithProfile();

  return useMutation({
    mutationFn: (id: string) => {
      if (!user?.id) {
        return Promise.reject(new Error('User not found'));
      }
      return deletePMACertificate(id, user.id);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [PMA_CERTIFICATES_QUERY_KEY],
      });
      // Also invalidate PMA stats to ensure counts are updated
      queryClient.invalidateQueries({
        queryKey: [PMA_STATS_QUERY_KEY],
      });
    },
  });
}

export function useRestorePMACertificate() {
  const queryClient = useQueryClient();
  const { data: user } = useUserWithProfile();

  return useMutation({
    mutationFn: (id: string) => {
      if (!user?.id) {
        return Promise.reject(new Error('User not found'));
      }
      return restorePMACertificate(id, user.id);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [PMA_CERTIFICATES_QUERY_KEY],
      });
      // Also invalidate PMA stats to ensure counts are updated
      queryClient.invalidateQueries({
        queryKey: [PMA_STATS_QUERY_KEY],
      });
    },
  });
}

export function usePermanentlyDeletePMACertificate() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => permanentlyDeletePMACertificate(id),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [PMA_CERTIFICATES_QUERY_KEY],
      });
      // Also invalidate PMA stats to ensure counts are updated
      queryClient.invalidateQueries({
        queryKey: [PMA_STATS_QUERY_KEY],
      });
    },
  });
}

export function useSoftDeletedPMACertificates(projectId: string | null) {
  return useQuery({
    queryKey: ['soft-deleted-pma-certificates', projectId],
    queryFn: () => getSoftDeletedPMACertificates(projectId!),
    enabled: !!projectId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}
