'use client';

import { Button } from '@/components/ui/button';
import { usePermissions } from '@/hooks/use-permissions';
import { Plus } from 'lucide-react';
import { useTranslations } from 'next-intl';
import Link from 'next/link';

export function CreateProjectButton() {
  const common = useTranslations('common');
  const { isContractor } = usePermissions();

  // Only contractors can create projects, admins can only be invited
  if (!isContractor) {
    return null;
  }

  return (
    <Button size="lg" className="w-full sm:w-auto shrink-0" asChild>
      <Link href="/projects/create">
        <Plus className="h-4 w-4 mr-2" />
        {common('new')}
      </Link>
    </Button>
  );
}
