'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { Combobox } from '@/components/ui/combobox';
import { CreatableCombobox } from '@/components/ui/creatable-combobox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { SectionHeader } from '@/components/ui/section-header';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { zodResolver } from '@hookform/resolvers/zod';
import { Calendar, ChevronRight, HelpCircle, MapPin, User } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useForm, UseFormReturn } from 'react-hook-form';
import { MALAYSIAN_STATES } from '../constants/project-constants';
import {
  useAgencies,
  useCreateAgency,
  useJkrUsers,
} from '../hooks/use-projects';
import {
  projectDetailsSchema,
  type ProjectDetailsSchema,
} from '../schemas/project-schema';

// No longer need hard-coded data here

interface ProjectDetailsStepProps {
  form: UseFormReturn<ProjectDetailsSchema>;
  onNext: (data: ProjectDetailsSchema) => void;
  onCancel?: () => void;
  isLoading?: boolean;
}

/**
 * Project Details Step - First step in the multi-step project creation form
 * Handles basic project information like name, code, agency, dates, etc.
 */
export function ProjectDetailsStep({
  form,
  onNext,
  onCancel,
  isLoading,
}: ProjectDetailsStepProps) {
  const t = useTranslations('pages.projects.create.form');
  const stateT = useTranslations('states');
  const actionsT = useTranslations('pages.projects.create.form.actions');

  // Get the selected state to filter JKR users
  const selectedState = form.watch('state');

  // Fetch agencies and JKR users for dropdowns
  const { data: agencies = [] } = useAgencies();
  const { data: jkrUsers = [] } = useJkrUsers(selectedState || undefined);
  const createAgencyMutation = useCreateAgency();

  // Handle agency creation
  const handleCreateAgency = async (agencyName: string) => {
    try {
      const newAgency = await createAgencyMutation.mutateAsync(agencyName);
      // Set the new agency as selected
      form.setValue('agency_id', newAgency.id);
      form.setValue('agency_name', newAgency.name);
    } catch (error) {
      console.error('Failed to create agency:', error);
      // Optionally show error toast
    }
  };

  // Get current agency selection
  const currentAgencyId = form.watch('agency_id');

  // Determine the display value for the combobox
  const getAgencyDisplayValue = () => {
    if (currentAgencyId) {
      const agency = agencies.find((a) => a.id === currentAgencyId);
      return agency ? agency.id : '';
    }
    return '';
  };

  const onSubmit = (data: ProjectDetailsSchema) => {
    onNext(data);
  };

  const onError = (_errors: object) => {};

  return (
    <form onSubmit={form.handleSubmit(onSubmit, onError)} className="space-y-7">
      <SectionHeader number={1} title={t('sections.projectDetails')} />

      {/* Basic Information */}
      <div className="grid gap-6 md:grid-cols-2">
        <div className="space-y-2">
          <Label htmlFor="name">
            {t('fields.name.label')} <span className="text-destructive">*</span>
          </Label>
          <Input
            id="name"
            placeholder={t('fields.name.placeholder')}
            {...form.register('name')}
          />
          {form.formState.errors.name && (
            <p className="text-sm text-destructive">
              {form.formState.errors.name.message}
            </p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="code" className="flex items-center gap-2">
            {t('fields.code.label')} <span className="text-destructive">*</span>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                </TooltipTrigger>
                <TooltipContent>
                  <p>
                    Enter the quotation number from your tender document (can be
                    more than 20 characters).
                  </p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </Label>
          <Input
            id="code"
            placeholder={t('fields.code.placeholder')}
            {...form.register('code')}
          />
          {form.formState.errors.code && (
            <p className="text-sm text-destructive">
              {form.formState.errors.code.message}
            </p>
          )}
        </div>
      </div>

      {/* Agency & State Selection */}
      <div className="space-y-6">
        <div className="space-y-2">
          <Label htmlFor="agency_id" className="flex items-center gap-2">
            <MapPin className="h-4 w-4" />
            {t('fields.agency.label')}{' '}
            <span className="text-destructive">*</span>
          </Label>
          <CreatableCombobox
            options={agencies.map((agency) => ({
              value: agency.id,
              label: agency.name,
              searchValue: `${agency.name} ${agency.state || ''}`,
              content: (
                <div className="flex flex-col">
                  <span className="font-medium">{agency.name}</span>
                  {agency.state && (
                    <span className="text-xs text-muted-foreground">
                      {agency.state}
                    </span>
                  )}
                </div>
              ),
            }))}
            value={getAgencyDisplayValue()}
            onValueChange={(value) => {
              // Clear agency_name when selecting existing agency
              form.setValue('agency_id', value);
              form.setValue('agency_name', '');
            }}
            onCreateNew={(agencyName) => {
              // Clear agency_id when creating new agency
              form.setValue('agency_id', '');
              form.setValue('agency_name', agencyName);
              handleCreateAgency(agencyName);
            }}
            placeholder={t('fields.agency.placeholder')}
            searchPlaceholder={t('fields.agency.searchPlaceholder')}
            emptyMessage={t('fields.agency.emptyMessage')}
            createMessage={t('fields.agency.createNew')}
            disabled={createAgencyMutation.isPending}
          />
          {form.formState.errors.agency_id && (
            <p className="text-sm text-destructive">
              {form.formState.errors.agency_id.message}
            </p>
          )}
        </div>

        {/* State Selection */}
        <div className="space-y-2">
          <Label htmlFor="state" className="flex items-center gap-2">
            <MapPin className="h-4 w-4" />
            {t('fields.state.label')}{' '}
            <span className="text-destructive">*</span>
          </Label>
          <Select
            value={form.watch('state') || ''}
            onValueChange={(value) => {
              // Clear JKR PIC selection when state changes
              form.setValue('jkr_pic_id', '');
              if (value && value !== '') {
                form.setValue('state', value as ProjectDetailsSchema['state']);
              }
            }}
          >
            <SelectTrigger>
              <SelectValue placeholder={t('fields.state.placeholder')} />
            </SelectTrigger>
            <SelectContent>
              {MALAYSIAN_STATES.map((state) => (
                <SelectItem key={state.value} value={state.value}>
                  {stateT(state.value)}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {form.formState.errors.state && (
            <p className="text-sm text-destructive">
              {form.formState.errors.state.message}
            </p>
          )}
        </div>

        {/* Location */}
        <div className="space-y-2">
          <Label htmlFor="location" className="flex items-center gap-2">
            <MapPin className="h-4 w-4" />
            {t('fields.location.label')}{' '}
            <span className="text-destructive">*</span>
          </Label>
          <Input
            id="location"
            placeholder={t('fields.location.placeholder')}
            {...form.register('location')}
          />
          {form.formState.errors.location && (
            <p className="text-sm text-destructive">
              {form.formState.errors.location.message}
            </p>
          )}
        </div>
      </div>

      {/* JKR Person in Charge */}
      <div className="space-y-2">
        <Label htmlFor="jkr_pic_id" className="flex items-center gap-2">
          <User className="h-4 w-4" />
          {t('fields.personInCharge.label')}{' '}
          <span className="text-destructive">*</span>
        </Label>
        {!selectedState && (
          <p className="text-sm text-muted-foreground bg-muted/50 p-2 rounded-md">
            {t('fields.personInCharge.noStateSelected')}
          </p>
        )}
        {selectedState && jkrUsers.length === 0 && (
          <p className="text-sm text-orange-600 bg-orange-50 p-2 rounded-md">
            {t('fields.personInCharge.noPersonnelFound')} (
            {stateT(selectedState)}). JKR Admins and JKR PICs assigned to this
            state will appear here.
          </p>
        )}
        {selectedState && jkrUsers.length > 0 && (
          <>
            <p className="text-xs text-muted-foreground">
              {t('fields.personInCharge.personnelFoundNote')}{' '}
              {stateT(selectedState)} ({jkrUsers.length} available)
            </p>
            <Combobox
              options={jkrUsers.map((user) => ({
                value: user.id,
                label: `${user.name} (${user.email})`,
                searchValue: `${user.name} ${user.email} ${user.admin_access_mode === 'project' ? 'Admin' : 'PIC'}`,
                content: (
                  <div className="flex flex-col">
                    <span className="font-medium">{user.name}</span>
                    <span className="text-xs text-muted-foreground">
                      {user.email} •{' '}
                      {user.admin_access_mode === 'project' ? 'Admin' : 'PIC'}
                    </span>
                  </div>
                ),
              }))}
              value={form.watch('jkr_pic_id') || ''}
              onValueChange={(value) => form.setValue('jkr_pic_id', value)}
              placeholder={t('fields.personInCharge.placeholder')}
              searchPlaceholder={t('fields.personInCharge.searchPlaceholder')}
              emptyMessage={t('fields.personInCharge.emptyMessage')}
            />
          </>
        )}
        {form.formState.errors.jkr_pic_id && (
          <p className="text-sm text-destructive">
            {form.formState.errors.jkr_pic_id?.message}
          </p>
        )}
      </div>

      {/* Dates */}
      <div className="grid gap-6 md:grid-cols-2">
        <div className="space-y-2">
          <Label htmlFor="start_date" className="flex items-center gap-2">
            <Calendar className="h-4 w-4" />
            {t('fields.startDate.label')}{' '}
            <span className="text-destructive">*</span>
          </Label>
          <Input id="start_date" type="date" {...form.register('start_date')} />
          {form.formState.errors.start_date && (
            <p className="text-sm text-destructive">
              {form.formState.errors.start_date?.message}
            </p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="end_date" className="flex items-center gap-2">
            <Calendar className="h-4 w-4" />
            {t('fields.endDate.label')}
          </Label>
          <Input
            id="end_date"
            type="date"
            min={form.watch('start_date')}
            {...form.register('end_date')}
          />
          {form.formState.errors.end_date && (
            <p className="text-sm text-destructive">
              {form.formState.errors.end_date?.message}
            </p>
          )}
        </div>
      </div>

      {/* Status field removed as requested */}

      {/* Actions */}
      <div className="flex gap-4 pt-6">
        <Button type="submit" disabled={isLoading} className="min-w-32">
          <ChevronRight className="h-4 w-4 mr-2" />
          {actionsT('next')}
        </Button>
        {onCancel && (
          <Button type="button" variant="outline" onClick={onCancel}>
            {actionsT('cancel')}
          </Button>
        )}
      </div>
    </form>
  );
}

/**
 * Hook to create and manage the project details form
 * @param initialData Optional initial data for the form
 * @returns Form instance and helper functions
 */
export function useProjectDetailsForm(
  initialData?: Partial<ProjectDetailsSchema>,
) {
  return useForm<ProjectDetailsSchema>({
    resolver: zodResolver(projectDetailsSchema),
    defaultValues: {
      name: initialData?.name || '',
      code: initialData?.code || '', // Will be auto-generated if empty
      agency_id: initialData?.agency_id || '',
      agency_name: initialData?.agency_name || '',
      jkr_pic_id: initialData?.jkr_pic_id || '',
      location: initialData?.location || '',
      state:
        (initialData?.state as
          | 'JH'
          | 'KD'
          | 'KT'
          | 'ML'
          | 'NS'
          | 'PH'
          | 'PN'
          | 'PK'
          | 'PL'
          | 'SB'
          | 'SW'
          | 'SL'
          | 'TR'
          | 'WP'
          | 'LBN'
          | 'PW'
          | 'OTH'
          | undefined) || undefined,
      start_date: initialData?.start_date || '',
      end_date: initialData?.end_date || '',
      description: initialData?.description || '',
      status:
        (initialData?.status as
          | 'pending'
          | 'active'
          | 'completed'
          | 'cancelled') || 'pending',
    },
  });
}
