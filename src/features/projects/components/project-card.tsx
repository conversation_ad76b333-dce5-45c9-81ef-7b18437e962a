'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { useProjectContext } from '@/providers/project-context';
import {
  Building2,
  Calendar,
  MapPin,
  MoreVertical,
  Shield,
  User,
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import { ProjectCardProps } from '../types/project';
import { calculateProjectDuration, formatDate } from '../utils/project-utils';

/**
 * Individual project card component
 */
export const ProjectCard: React.FC<ProjectCardProps> = ({
  project,
  onEdit: _onEdit,
  onDelete: _onDelete,
}) => {
  const { selectProject, setSelectedProject } = useProjectContext();
  const router = useRouter();

  const handleCardClick = () => {
    selectProject(project.id);
    setSelectedProject(project);
    router.push('/dashboard');
  };

  return (
    <Card
      className="border-0 shadow-sm hover:shadow-md transition-shadow cursor-pointer"
      onClick={handleCardClick}
    >
      <CardHeader className="pb-2 sm:pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-start space-x-2 sm:space-x-3 flex-1 min-w-0">
            <div className="p-1.5 sm:p-2 bg-primary/10 rounded-lg">
              <Building2 className="h-4 w-4 sm:h-5 sm:w-5 text-primary" />
            </div>
            <div className="flex-1 min-w-0">
              <CardTitle className="text-base sm:text-lg font-semibold text-gray-900 line-clamp-2">
                {project.name}
              </CardTitle>
              <CardDescription className="flex items-center mt-1">
                <span className="font-medium text-primary text-sm">{project.code}</span>
              </CardDescription>
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            className="h-8 w-8 p-0 flex-shrink-0"
            onClick={(e) => e.stopPropagation()}
          >
            <MoreVertical className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-2 sm:space-y-3">
          {/* Location */}
          <div className="flex items-center text-xs sm:text-sm text-gray-600">
            <MapPin className="h-3 w-3 sm:h-4 sm:w-4 mr-1.5 sm:mr-2 text-gray-400 flex-shrink-0" />
            <span className="line-clamp-1">{project.location}</span>
          </div>

          {/* Dates */}
          <div className="flex items-start text-xs sm:text-sm text-gray-600">
            <Calendar className="h-3 w-3 sm:h-4 sm:w-4 mr-1.5 sm:mr-2 text-gray-400 flex-shrink-0 mt-0.5" />
            <span className="line-clamp-2">
              {formatDate(project.start_date)}
              {project.end_date && project.start_date && (
                <>
                  {' → '}
                  {formatDate(project.end_date)}
                  <span className="ml-1 sm:ml-2 text-gray-500 block sm:inline">
                    (
                    {calculateProjectDuration(
                      project.start_date,
                      project.end_date,
                    )}
                    )
                  </span>
                </>
              )}
            </span>
          </div>

          {/* Agency */}
          {project.agency && (
            <div className="flex items-center text-xs sm:text-sm text-gray-600">
              <Shield className="h-3 w-3 sm:h-4 sm:w-4 mr-1.5 sm:mr-2 text-gray-400 flex-shrink-0" />
              <span className="line-clamp-1">
                {project.agency.name}
                {project.agency.state && ` (${project.agency.state})`}
              </span>
            </div>
          )}

          {/* JKR PICs - Now showing admin users from project_users */}
          {project.project_users &&
            project.project_users.filter((pu) => pu.user.user_role === 'admin')
              .length > 0 && (
              <div className="flex items-start text-xs sm:text-sm text-gray-600">
                <User className="h-3 w-3 sm:h-4 sm:w-4 mr-1.5 sm:mr-2 mt-0.5 text-gray-400 flex-shrink-0" />
                <div className="flex-1 min-w-0">
                  <span className="text-gray-500 text-xs uppercase tracking-wide">
                    JKR PIC:
                  </span>
                  <div className="mt-1 space-y-1">
                    {project.project_users
                      .filter((pu) => pu.user.user_role === 'admin')
                      .slice(0, 2)
                      .map((pic, index) => (
                        <div key={index} className="text-xs truncate">
                          {pic.user.name}
                        </div>
                      ))}
                    {project.project_users.filter(
                      (pu) => pu.user.user_role === 'admin',
                    ).length > 2 && (
                        <div className="text-xs text-gray-500">
                          +
                          {project.project_users.filter(
                            (pu) => pu.user.user_role === 'admin',
                          ).length - 2}{' '}
                          more
                        </div>
                      )}
                  </div>
                </div>
              </div>
            )}
        </div>
      </CardContent>
    </Card>
  );
};
