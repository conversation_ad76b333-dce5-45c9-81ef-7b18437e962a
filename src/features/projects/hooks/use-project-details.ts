// src/features/projects/hooks/use-project-details.ts

import { useQuery } from '@tanstack/react-query';
import { getProjectById } from '../services/project-service';
import type { Project } from '../types/project';

/**
 * Fetch project details by ID using TanStack Query.
 * Handles loading, error, and data states.
 *
 * @param projectId - The ID of the project to fetch.
 * @returns TanStack Query result for project details.
 */
export function useProjectDetails(projectId: string) {
  return useQuery<Project | null, Error>({
    queryKey: ['project-details', projectId],
    queryFn: () => getProjectById(projectId),
    enabled: !!projectId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}
