'use client';

import { Button } from '@/components/ui/button';
import { useAuthTranslations } from '@/hooks/use-translations';
import { cn } from '@/lib/utils';
import { CheckCircle, Mail } from 'lucide-react';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import * as React from 'react';

interface CheckEmailVerificationProps
  extends React.HTMLAttributes<HTMLDivElement> {
  className?: string;
}

export function CheckEmailVerification({
  className,
  ...props
}: CheckEmailVerificationProps) {
  const auth = useAuthTranslations();
  const router = useRouter();
  const searchParams = useSearchParams();

  // Get email from URL params (passed from registration)
  const email = searchParams?.get('email') || '';
  const userType = searchParams?.get('type') || 'contractor';

  const handleBackToLogin = () => {
    const loginPath =
      userType === 'admin' ? '/admin/login' : '/contractor/login';
    router.push(loginPath);
  };

  return (
    <div className={cn('flex flex-col gap-6', className)} {...props}>
      <div className="flex flex-col items-center gap-4 text-center">
        <div className="bg-green-100 text-green-600 rounded-full p-3">
          <CheckCircle className="h-8 w-8" />
        </div>
        <div className="space-y-2">
          <h1 className="text-2xl font-bold">{auth('checkYourEmail')}</h1>
          <p className="text-muted-foreground text-sm max-w-sm">
            {auth('emailVerificationSent')}
          </p>
        </div>
      </div>

      {email && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center gap-3">
            <Mail className="h-5 w-5 text-blue-600" />
            <div className="flex-1">
              <p className="text-sm font-medium text-blue-900">
                {auth('emailSentTo')}
              </p>
              <p className="text-sm text-blue-700 font-mono">{email}</p>
            </div>
          </div>
        </div>
      )}

      <div className="space-y-4">
        <div className="text-center text-sm text-muted-foreground">
          <p>{auth('emailVerificationInstructions')}</p>
        </div>

        <div className="space-y-3">
          {/* <Button
            onClick={handleResendEmail}
            variant="outline"
            className="w-full"
            disabled={isResending || !email}
          >
            {isResending ? (
              <>
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                {auth('resendingEmail')}
              </>
            ) : (
              <>
                <Mail className="mr-2 h-4 w-4" />
                {auth('resendEmail')}
              </>
            )}
          </Button> */}

          <Button
            onClick={handleBackToLogin}
            variant="ghost"
            className="w-full"
          >
            {auth('backToLogin')}
          </Button>
        </div>

        <div className="text-center text-xs text-muted-foreground">
          <p>{auth('emailNotReceived')}</p>
          <p className="mt-1">
            {auth('checkSpamFolder')} •{' '}
            <Link href="/contact" className="text-primary hover:underline">
              {auth('contactSupport')}
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
}
