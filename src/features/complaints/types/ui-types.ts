// UI types aligned with database schema
// These map directly to the database complaint structure

export interface ComplaintUI {
  id: string;
  // Database fields - Section A (Basic Information)
  email: string;
  number: string;
  date: string; // damage complaint date
  expected_completion_date: string;
  contractor_name: string | null;
  location: string | null;
  no_pma_lif: string | null;
  description: string | null;
  involves_mantrap: boolean | null;

  // Database fields - Section B (Repair Information)
  actual_completion_date?: string | null;
  repair_completion_time?: string | null;
  cause_of_damage?: string | null;
  correction_action?: string | null;
  proof_of_repair_urls?: string[] | null;
  repair_cost: number | null;

  // Status and metadata
  status: 'open' | 'on_hold' | 'closed';
  follow_up: 'in_progress' | 'pending_approval' | 'verified';
  created_at: string | null;

  // Section completion tracking (computed)
  sectionACompleted: boolean;
  sectionBCompleted: boolean;

  // Display helpers for files
  proofOfRepairFiles?: Array<{
    name: string;
    url: string;
    size: number;
  }>;
}

export interface ComplaintTableProps {
  onViewComplaint: (complaint: ComplaintUI) => void;
  onEditComplaint?: (complaint: ComplaintUI) => void;
}
