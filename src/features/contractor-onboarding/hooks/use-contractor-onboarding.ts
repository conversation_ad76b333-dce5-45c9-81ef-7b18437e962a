import { useUserWithProfile } from '@/hooks/use-auth';
import { supabase } from '@/lib/supabase';
import { generateCompanyCode } from '@/lib/utils';
import { zodResolver } from '@hookform/resolvers/zod';
import { useQueryClient } from '@tanstack/react-query';
import { useCallback, useEffect, useState } from 'react';
import { SubmitHandler, useForm } from 'react-hook-form';
import { toast } from 'sonner';
import {
  step1Schema,
  step2Schema,
  step3Schema,
  type ContractorFullFormValues,
  type ContractorStep1FormValues,
  type ContractorStep2FormValues,
  type ContractorStep3FormValues,
} from '../schemas/contractor-onboarding-schemas';

export interface UseContractorOnboardingProps {
  onSubmit?: (values: ContractorFullFormValues) => void | Promise<void>;
  onSkipToCompletion?: () => void | Promise<void>;
}

export function useContractorOnboarding({
  onSubmit,
  onSkipToCompletion,
}: UseContractorOnboardingProps) {
  const { data: user } = useUserWithProfile();
  const queryClient = useQueryClient();
  const [currentStep, setCurrentStep] = useState(1);
  const [step1Data, setStep1Data] = useState<ContractorStep1FormValues | null>(
    null,
  );
  const [step2Data, setStep2Data] = useState<ContractorStep2FormValues | null>(
    null,
  );
  const [isCodeCopied, setIsCodeCopied] = useState<boolean>(false);

  // Form instances
  const step1Form = useForm<ContractorStep1FormValues>({
    resolver: zodResolver(step1Schema),
    defaultValues: {
      fullName: '',
      icNumber: '',
      phoneNumber: '',
    },
  });

  const step2Form = useForm<ContractorStep2FormValues>({
    resolver: zodResolver(step2Schema),
    defaultValues: {
      companyRegistrationType: 'create',
      specialCode: '',
    },
  });

  const step3Form = useForm<ContractorStep3FormValues>({
    resolver: zodResolver(step3Schema),
    defaultValues: {
      company_name: '',
      company_type: undefined,
      company_hotline: '',
      oem_name: '',
      code: '',
      competent_persons: [
        {
          name: '',
          ic_no: '',
          phone_no: '',
          address: '',
          cp_type: undefined,
          cp_registeration_no: '',
          cp_registeration_cert: '',
          cert_exp_date: '',
          no_of_pma: 0,
        },
      ],
    },
  });

  // Watch values
  const companyRegistrationType = step2Form.watch('companyRegistrationType');
  const watchedCompanyType = step3Form.watch('company_type');

  // Initialize company code when step 3 is reached
  useEffect(() => {
    if (currentStep === 3 && !step3Form.getValues('code')) {
      step3Form.setValue('code', generateCompanyCode());
    }
  }, [currentStep, step3Form]);

  // Step submit handlers
  const handleStep1Submit: SubmitHandler<ContractorStep1FormValues> =
    useCallback(
      async (values) => {
        setStep1Data(values);

        // Check if user already has contractor_id
        if (user?.profile?.contractor_id) {
          try {
            // Update user's personal information and mark onboarding as completed
            const { error: updateError } = await supabase
              .from('users')
              .update({
                name: values.fullName,
                phone_number: values.phoneNumber,
                onboarding_completed: true,
                updated_at: new Date().toISOString(),
              })
              .eq('id', user.id);

            if (updateError) {
              console.error('Error updating user profile:', updateError);
              toast.error('Failed to update profile. Please try again.');
              return;
            }

            // Invalidate queries to refresh user data
            queryClient.invalidateQueries({ queryKey: ['user-with-profile'] });
            queryClient.invalidateQueries({ queryKey: ['user'] });

            toast.success(
              'Personal information saved! You already belong to a company.',
            );

            // Skip to completion since user already has a company
            if (onSkipToCompletion) {
              await onSkipToCompletion();
            }
          } catch (error) {
            console.error('Error updating profile:', error);
            toast.error('Failed to update profile. Please try again.');
          }
          return;
        }

        // Normal flow - proceed to company setup
        setCurrentStep(2);
        toast.success('Personal information saved!');
      },
      [user?.profile?.contractor_id, user?.id, onSkipToCompletion, queryClient],
    );

  const handleStep2Submit: SubmitHandler<ContractorStep2FormValues> =
    useCallback(
      async (values) => {
        if (!step1Data) {
          toast.error('Please complete step 1 first');
          setCurrentStep(1);
          return;
        }

        setStep2Data(values);

        try {
          if (values.companyRegistrationType === 'create') {
            // Move to step 3 for company creation
            setCurrentStep(3);
            toast.success('Proceeding to company creation...');
          } else if (values.companyRegistrationType === 'join') {
            // Handle joining existing company - complete the flow
            const fullData: ContractorFullFormValues = {
              ...step1Data,
              ...values,
            };
            if (onSubmit) {
              await onSubmit(fullData);
            } else {
              console.log('Joining company with code:', values.specialCode);
              toast.success('Successfully joined the company!');
            }
          }
        } catch (error) {
          const errorMessage =
            error instanceof Error
              ? error.message
              : 'An error occurred. Please try again.';
          toast.error(errorMessage);
        }
      },
      [step1Data, onSubmit],
    );

  const handleStep3Submit: SubmitHandler<ContractorStep3FormValues> =
    useCallback(
      async (values) => {
        if (!step1Data || !step2Data) {
          toast.error('Please complete previous steps first');
          setCurrentStep(1);
          return;
        }

        const fullData: ContractorFullFormValues = {
          ...step1Data,
          ...step2Data,
          ...values,
        };

        try {
          if (onSubmit) {
            await onSubmit(fullData);
          } else {
            console.log('Company created with data:', values);
            toast.success('Company created successfully!');
          }
        } catch (error) {
          const errorMessage =
            error instanceof Error
              ? error.message
              : 'An error occurred. Please try again.';
          toast.error(errorMessage);
        }
      },
      [step1Data, step2Data, onSubmit],
    );

  // Navigation handlers
  const goToPreviousStep = useCallback(() => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  }, [currentStep]);

  // Company code handlers
  const regenerateCode = useCallback(() => {
    const newCode = generateCompanyCode();
    step3Form.setValue('code', newCode);
  }, [step3Form]);

  const handleCopyCode = useCallback(async () => {
    try {
      const code = step3Form.getValues('code');
      await navigator.clipboard.writeText(code);
      setIsCodeCopied(true);
      setTimeout(() => setIsCodeCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy code:', err);
    }
  }, [step3Form]);

  return {
    // State
    currentStep,
    step1Data,
    step2Data,
    isCodeCopied,

    // Forms
    step1Form,
    step2Form,
    step3Form,

    // Watched values
    companyRegistrationType,
    watchedCompanyType,

    // Handlers
    handleStep1Submit,
    handleStep2Submit,
    handleStep3Submit,
    goToPreviousStep,
    regenerateCode,
    handleCopyCode,
  };
}
