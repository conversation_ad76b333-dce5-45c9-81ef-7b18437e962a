import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { SectionHeader } from '@/components/ui/section-header';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useCheckCompanyNameAvailability } from '@/hooks/use-company-name-availability';
import {
  useCommonTranslations,
  useCompanyTranslations,
  useContractorTranslations,
} from '@/hooks/use-translations';
import {
  AlertCircle,
  Check,
  CheckCircle,
  ChevronLeft,
  Copy,
  Plus,
  RefreshCw,
} from 'lucide-react';
import React from 'react';
import { UseFormReturn, useFieldArray } from 'react-hook-form';
import { type ContractorStep3FormValues } from '../schemas/contractor-onboarding-schemas';
import { CompetentPersonForm } from './competent-person-form';

interface ContractorOnboardingStep4Props {
  form: UseFormReturn<ContractorStep3FormValues>;
  onSubmit: (values: ContractorStep3FormValues) => void;
  onPrevious: () => void;
  watchedCompanyType:
  | 'COMPETENT_FIRM'
  | 'NON_COMPETENT_FIRM'
  | 'OEM'
  | undefined;
  isCodeCopied: boolean;
  onCopyCode: () => void;
  onRegenerateCode: () => void;
}

export const ContractorOnboardingStep4 =
  React.memo<ContractorOnboardingStep4Props>(
    ({
      form,
      onSubmit,
      onPrevious,
      watchedCompanyType,
      isCodeCopied,
      onCopyCode,
      onRegenerateCode,
    }) => {
      const t = useContractorTranslations();
      const tCommon = useCommonTranslations();
      const tCompany = useCompanyTranslations();

      const companyName = form.watch('company_name');
      const { data: availabilityData, isLoading: checkingAvailability } =
        useCheckCompanyNameAvailability(companyName);

      // Competent persons field array
      const { fields, append, remove } = useFieldArray({
        control: form.control,
        name: 'competent_persons',
      });

      const addCompetentPerson = () => {
        append({
          name: '',
          ic_no: '',
          phone_no: '',
          address: '',
          cp_type: undefined,
          cp_registeration_no: '',
          cp_registeration_cert: '',
          cert_exp_date: '',
          no_of_pma: 0,
        });
      };

      return (
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6 sm:space-y-7">
            <SectionHeader number={3} title={t('onboarding.step4.title')} />

            <div className="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-2 gap-4 sm:gap-6 lg:gap-8">
              {/* Company Information Section */}
              <div className="col-span-1">
                <div className="space-y-4 sm:space-y-6">
                  {/* Auto-generated Company Code */}
                  <FormField
                    control={form.control}
                    name="code"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {t('onboarding.step4.companyCode')}
                        </FormLabel>
                        <div className="flex gap-2">
                          <FormControl>
                            <Input
                              {...field}
                              readOnly
                              className="font-mono text-center tracking-wider bg-muted"
                            />
                          </FormControl>
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={onCopyCode}
                            className="shrink-0"
                          >
                            {isCodeCopied ? (
                              <Check className="w-4 h-4" />
                            ) : (
                              <Copy className="w-4 h-4" />
                            )}
                          </Button>
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={onRegenerateCode}
                            className="shrink-0"
                          >
                            <RefreshCw className="w-4 h-4" />
                          </Button>
                        </div>
                        <p className="text-xs text-muted-foreground">
                          {t('onboarding.step4.companyCodeHelp')}
                        </p>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="company_name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {t('onboarding.step4.companyName')}{' '}
                          <span className="text-destructive">*</span>
                        </FormLabel>
                        <FormControl>
                          <div className="relative">
                            <Input
                              placeholder={t(
                                'onboarding.step4.companyNamePlaceholder',
                              )}
                              {...field}
                            />
                            {checkingAvailability && (
                              <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                                <RefreshCw className="h-4 w-4 animate-spin text-muted-foreground" />
                              </div>
                            )}
                            {availabilityData?.available === true && (
                              <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                                <CheckCircle className="h-4 w-4 text-green-600" />
                              </div>
                            )}
                            {availabilityData?.available === false && (
                              <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                                <AlertCircle className="h-4 w-4 text-red-600" />
                              </div>
                            )}
                          </div>
                        </FormControl>
                        <div className="space-y-1">
                          <p className="text-sm text-muted-foreground">
                            {t('onboarding.step4.companyNameHelp')}
                          </p>
                          {field.value && (
                            <p className="text-sm text-primary font-medium">
                              {t('onboarding.step4.companyNamePreview', {
                                name: field.value.trim().toUpperCase(),
                              })}
                            </p>
                          )}
                          {availabilityData?.message && (
                            <p
                              className={`text-sm font-medium ${availabilityData.available === true
                                ? 'text-green-600'
                                : availabilityData.available === false
                                  ? 'text-red-600'
                                  : 'text-muted-foreground'
                                }`}
                            >
                              {availabilityData.message}
                            </p>
                          )}
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="company_type"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {t('onboarding.step4.companyType')}{' '}
                          <span className="text-destructive">*</span>
                        </FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          value={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue
                                placeholder={t(
                                  'onboarding.step4.companyTypeHelp',
                                )}
                              />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="COMPETENT_FIRM">
                              {tCompany('types.competent_firm')}
                            </SelectItem>
                            <SelectItem value="NON_COMPETENT_FIRM">
                              {tCompany('types.non_competent_firm')}
                            </SelectItem>
                            <SelectItem value="OEM">
                              {tCompany('types.oem')}
                            </SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="company_hotline"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {t('onboarding.step4.companyHotline')}{' '}
                          <span className="text-destructive">*</span>
                        </FormLabel>
                        <FormControl>
                          <Input
                            type="tel"
                            placeholder={t(
                              'onboarding.step4.companyHotlinePlaceholder',
                            )}
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              {/* OEM Details Section - Only show if OEM is selected */}
              {watchedCompanyType === 'OEM' && (
                <div className="xl:col-span-1">
                  <div className="space-y-7">
                    <FormField
                      control={form.control}
                      name="oem_name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            {t('onboarding.step4.oemName')}{' '}
                            <span className="text-destructive">*</span>
                          </FormLabel>
                          <FormControl>
                            <Input
                              placeholder={t(
                                'onboarding.step4.oemNamePlaceholder',
                              )}
                              {...field}
                            />
                          </FormControl>
                          <p className="text-xs text-muted-foreground">
                            {t('onboarding.step4.oemNameHelp')}
                          </p>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
              )}

              {/* Appointed OEM/Competent Firm Section - Only show if NON_COMPETENT_FIRM is selected */}
              {watchedCompanyType === 'NON_COMPETENT_FIRM' && (
                <div className="xl:col-span-1">
                  <div className="space-y-7">
                    <FormField
                      control={form.control}
                      name="appointed_oem_competent_firm"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            {t('onboarding.step4.appointedOemCompetentFirm')}{' '}
                            <span className="text-destructive">*</span>
                          </FormLabel>
                          <FormControl>
                            <Input
                              placeholder={t(
                                'onboarding.step4.appointedOemCompetentFirmPlaceholder',
                              )}
                              {...field}
                            />
                          </FormControl>
                          <p className="text-xs text-muted-foreground">
                            {t(
                              'onboarding.step4.appointedOemCompetentFirmHelp',
                            )}
                          </p>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
              )}
            </div>

            {/* Competent Persons Section */}
            <div className="space-y-4 sm:space-y-6">
              <div className="flex flex-wrap justify-between items-center gap-4">
                <div>
                  <h3 className="text-lg font-semibold">
                    {t('onboarding.step4.competentPersons.title')}
                  </h3>
                  <p className="text-sm text-muted-foreground">
                    {t('onboarding.step4.competentPersons.subtitle')}
                  </p>
                </div>
                <Button
                  type="button"
                  variant="outline"
                  onClick={addCompetentPerson}
                  className="flex items-center gap-2"
                >
                  <Plus className="w-4 h-4" />
                  {t('onboarding.step4.competentPersons.addButton')}
                </Button>
              </div>

              <div className="space-y-3 sm:space-y-4">
                {fields.map((field, index) => (
                  <CompetentPersonForm
                    key={field.id}
                    form={form}
                    index={index}
                    onRemove={remove}
                    canRemove={fields.length > 1}
                  />
                ))}
              </div>
            </div>

            <div className="flex flex-wrap justify-between gap-4 pt-4 sm:pt-6">
              <Button
                type="button"
                variant="outline"
                onClick={onPrevious}
                className="px-6 py-3"
              >
                <ChevronLeft className="w-4 h-4 mr-2" />
                {tCommon('back')}
              </Button>

              <Button
                type="submit"
                className="px-8 py-3"
                disabled={form.formState.isSubmitting}
              >
                {form.formState.isSubmitting
                  ? t('onboarding.step4.creatingButton')
                  : t('onboarding.step4.createButton')}
              </Button>
            </div>
          </form>
        </Form>
      );
    },
  );

ContractorOnboardingStep4.displayName = 'ContractorOnboardingStep4';
