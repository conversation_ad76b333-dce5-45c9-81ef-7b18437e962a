import { z } from 'zod';

// Competent Person Schema
export const competentPersonSchema = z.object({
  name: z.string().optional(),
  ic_no: z.string().optional(),
  phone_no: z.string().optional(),
  address: z.string().optional(),
  cp_type: z.enum(['CP1', 'CP2', 'CP3']).optional(),
  cp_registeration_no: z.string().optional(),
  cp_registeration_cert: z.string().optional(), // File URL
  cert_exp_date: z.string().optional(), // Date string
  no_of_pma: z.number().int().min(0).default(0).optional(),
});

// Step 1 Schema - Personal Information
export const step1Schema = z.object({
  fullName: z
    .string()
    .min(1, 'Full name is required')
    .min(2, 'Full name must be at least 2 characters')
    .max(100, 'Full name must not exceed 100 characters'),
  icNumber: z
    .string()
    .min(1, 'IC Number is required')
    .regex(/^\d{6}-\d{2}-\d{4}$/, 'IC Number must be in format 123456-78-9012'),
  phoneNumber: z
    .string()
    .min(1, 'Phone number is required')
    .regex(
      /^(\+?6?01[0-46-9]-*[0-9]{7,8})$/,
      'Please enter a valid Malaysian phone number',
    ),
});

// Step 2 Schema - Company Setup
export const step2Schema = z
  .object({
    companyRegistrationType: z.enum(['create', 'join'], {
      required_error: 'Please select a company registration option',
    }),
    specialCode: z.string().optional(),
  })
  .refine(
    (data) => {
      if (data.companyRegistrationType === 'join') {
        return data.specialCode && data.specialCode.trim().length > 0;
      }
      return true;
    },
    {
      message: 'Special code is required when joining a company',
      path: ['specialCode'],
    },
  );

// Step 3 Schema - Company Creation (only when "create" is selected)
export const step3Schema = z
  .object({
    company_name: z
      .string()
      .min(2, {
        message: 'Company name must be at least 2 characters.',
      })
      .max(100, {
        message: 'Company name must not exceed 100 characters.',
      })
      .transform((val) => val.trim().toUpperCase()), // Transform to uppercase
    company_type: z.enum(['COMPETENT_FIRM', 'NON_COMPETENT_FIRM', 'OEM'], {
      required_error: 'Please select a company type.',
    }),
    company_hotline: z
      .string()
      .min(8, {
        message: 'Company hotline must be at least 8 characters.',
      })
      .max(20, {
        message: 'Company hotline must not exceed 20 characters.',
      }),
    oem_name: z.string().optional(),
    appointed_oem_competent_firm: z.string().optional(),
    code: z.string().regex(/^\d{4}-[A-Z0-9]{4}-[A-Z0-9]{4}$/, {
      message: 'Company code must be in format YYMM-XXXX-XXXX.',
    }),
    competent_persons: z.array(competentPersonSchema).default([]),
  })
  .refine(
    (data) => {
      // If company type is OEM, oem_name is required
      if (data.company_type === 'OEM') {
        return data.oem_name && data.oem_name.length >= 2;
      }
      return true;
    },
    {
      message: 'OEM name is required for OEM companies.',
      path: ['oem_name'],
    },
  )
  .refine(
    (data) => {
      // If company type is NON_COMPETENT_FIRM, appointed_oem_competent_firm is required
      if (data.company_type === 'NON_COMPETENT_FIRM') {
        return (
          data.appointed_oem_competent_firm &&
          data.appointed_oem_competent_firm.length >= 2
        );
      }
      return true;
    },
    {
      message:
        'Appointed OEM/Competent Firm is required for non-competent firms.',
      path: ['appointed_oem_competent_firm'],
    },
  );

// Company form schema - same as step3 but without the uppercase transform for standalone use
export const companyFormSchema = z
  .object({
    company_name: z
      .string()
      .min(2, {
        message: 'Company name must be at least 2 characters.',
      })
      .max(100, {
        message: 'Company name must not exceed 100 characters.',
      }),
    company_type: z.enum(['COMPETENT_FIRM', 'NON_COMPETENT_FIRM', 'OEM'], {
      required_error: 'Please select a company type.',
    }),
    company_hotline: z
      .string()
      .min(8, {
        message: 'Company hotline must be at least 8 characters.',
      })
      .max(20, {
        message: 'Company hotline must not exceed 20 characters.',
      }),
    oem_name: z.string().optional(),
    appointed_oem_competent_firm: z.string().optional(),
    code: z.string().regex(/^\d{4}-[A-Z0-9]{4}-[A-Z0-9]{4}$/, {
      message: 'Company code must be in format YYMM-XXXX-XXXX.',
    }),
  })
  .refine(
    (data) => {
      // If company type is OEM, oem_name is required
      if (data.company_type === 'OEM') {
        return data.oem_name && data.oem_name.length >= 2;
      }
      return true;
    },
    {
      message: 'OEM name is required for OEM companies.',
      path: ['oem_name'],
    },
  )
  .refine(
    (data) => {
      // If company type is NON_COMPETENT_FIRM, appointed_oem_competent_firm is required
      if (data.company_type === 'NON_COMPETENT_FIRM') {
        return (
          data.appointed_oem_competent_firm &&
          data.appointed_oem_competent_firm.length >= 2
        );
      }
      return true;
    },
    {
      message:
        'Appointed OEM/Competent Firm is required for non-competent firms.',
      path: ['appointed_oem_competent_firm'],
    },
  );

// Type definitions for contractor onboarding
export type CompetentPersonFormValues = z.infer<typeof competentPersonSchema>;
export type ContractorStep1FormValues = z.infer<typeof step1Schema>;
export type ContractorStep2FormValues = z.infer<typeof step2Schema>;
export type ContractorStep3FormValues = z.infer<typeof step3Schema>;
export type ContractorFullFormValues = ContractorStep1FormValues &
  ContractorStep2FormValues &
  Partial<ContractorStep3FormValues>;

// Company form types
export type CompanyFormValues = z.infer<typeof companyFormSchema>;

// Schema aliases for backward compatibility
export const step3Schema_old = step2Schema; // Current step3 becomes step2
export const step4Schema = step3Schema; // Current step4 becomes step3

// Legacy type aliases for backward compatibility (to be removed after refactor)
export type Step1FormValues = ContractorStep1FormValues;
export type Step2FormValues = ContractorStep2FormValues;
export type Step3FormValues = ContractorStep3FormValues;
export type Step4FormValues = ContractorStep3FormValues; // For backward compatibility
export type ContractorStep4FormValues = ContractorStep3FormValues; // For backward compatibility
export type FullFormValues = ContractorFullFormValues;
