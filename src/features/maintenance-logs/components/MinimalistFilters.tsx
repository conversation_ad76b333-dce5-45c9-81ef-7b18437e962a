'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { ColumnVisibilityToggle } from '@/components/ui/ColumnVisibilityToggle';
import { Input } from '@/components/ui/input';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { cn } from '@/lib/utils';
import { format } from 'date-fns';
import { Calendar as CalendarIcon, Filter, Search, X } from 'lucide-react';
import { useEffect, useState } from 'react';
import {
  MAINTENANCE_STATUS,
  OPERATION_LOG_TYPES,
} from '../schemas/create-maintenance-log';
import type {
  MaintenanceLogsFilters,
  MaintenanceTableState,
} from '../types/table';
import { ALL_COLUMNS } from '../types/table';

interface MinimalistFiltersProps {
  filters: MaintenanceLogsFilters;
  onFilterChange: (filters: MaintenanceLogsFilters) => void;
  tableState: MaintenanceTableState;
  onTableStateChange: (state: MaintenanceTableState) => void;
  isLoading?: boolean;
}

export function MinimalistFilters({
  filters,
  onFilterChange,
  tableState,
  onTableStateChange,
  isLoading: _isLoading,
}: MinimalistFiltersProps) {
  const [searchValue, setSearchValue] = useState(filters.search || '');
  const [isFiltersOpen, setIsFiltersOpen] = useState(false);

  // Debounced search
  useEffect(() => {
    const timer = setTimeout(() => {
      onFilterChange({ ...filters, search: searchValue });
    }, 300);
    return () => clearTimeout(timer);
  }, [searchValue, filters, onFilterChange]);

  const activeFiltersCount = Object.entries(filters).filter(([key, value]) => {
    if (key === 'search')
      return value && typeof value === 'string' && value.length > 0;
    if (key === 'dateRange')
      return (
        (value as { from?: Date; to?: Date })?.from ||
        (value as { from?: Date; to?: Date })?.to
      );
    return value && value !== 'all';
  }).length;

  const handleClearFilters = () => {
    setSearchValue('');
    onFilterChange({
      contractors: [],
      operationType: undefined,
      status: undefined,
      search: '',
      dateRange: undefined,
      contractorId: undefined,
      pmaId: undefined,
    });
  };

  return (
    <div className="flex flex-col sm:flex-row sm:items-center gap-4">
      {/* Search Filter */}
      <div className="relative flex-1 max-w-md">
        <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
        <Input
          placeholder="Search by description, contractor, person in charge..."
          value={searchValue}
          onChange={(e) => setSearchValue(e.target.value)}
          className="pl-9 pr-10 border-gray-300 focus:border-gray-400 focus:ring-1 focus:ring-gray-400 rounded-xl h-11 bg-white/50 backdrop-blur-sm"
        />
        {searchValue && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setSearchValue('')}
            className="absolute right-2 top-1/2 h-7 w-7 -translate-y-1/2 p-0 hover:bg-gray-100 rounded-lg"
          >
            <X className="h-3 w-3" />
          </Button>
        )}
      </div>

      {/* Combined Filters */}
      <div className="flex items-center gap-3">
        {/* All Filters Combo */}
        <Popover open={isFiltersOpen} onOpenChange={setIsFiltersOpen}>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              className="h-11 px-4 border-gray-300 hover:border-gray-400 rounded-xl bg-white/50 backdrop-blur-sm"
            >
              <Filter className="h-4 w-4 mr-2" />
              Filters
              {activeFiltersCount > 0 && (
                <Badge
                  variant="secondary"
                  className="ml-2 h-5 px-2 text-xs bg-blue-100 text-blue-700 border-blue-200"
                >
                  {activeFiltersCount}
                </Badge>
              )}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-80 p-6" align="end">
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h4 className="font-semibold text-base text-gray-900">
                  Filter Options
                </h4>
                {activeFiltersCount > 0 && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleClearFilters}
                    className="h-auto p-0 text-sm text-gray-500 hover:text-gray-700"
                  >
                    Clear all
                  </Button>
                )}
              </div>

              {/* Status Filter */}
              <div className="space-y-3">
                <label className="text-sm font-medium text-gray-700">
                  Status
                </label>
                <Select
                  value={filters.status || 'all'}
                  onValueChange={(value) =>
                    onFilterChange({
                      ...filters,
                      status:
                        value === 'all'
                          ? undefined
                          : (value as (typeof MAINTENANCE_STATUS)[number]),
                    })
                  }
                >
                  <SelectTrigger className="w-full border-gray-300 focus:border-gray-400 rounded-lg">
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    {MAINTENANCE_STATUS.map((status) => (
                      <SelectItem key={status} value={status}>
                        <div className="flex items-center gap-2">
                          <div
                            className={cn(
                              'w-2 h-2 rounded-full',
                              status === 'fully function' && 'bg-emerald-500',
                              status === 'broken' && 'bg-red-500',
                            )}
                          />
                          <span className="capitalize">
                            {status.replace('_', ' ')}
                          </span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Operation Type Filter */}
              <div className="space-y-3">
                <label className="text-sm font-medium text-gray-700">
                  Operation Type
                </label>
                <Select
                  value={filters.operationType || 'all'}
                  onValueChange={(value) =>
                    onFilterChange({
                      ...filters,
                      operationType:
                        value === 'all'
                          ? undefined
                          : (value as (typeof OPERATION_LOG_TYPES)[number]),
                    })
                  }
                >
                  <SelectTrigger className="w-full border-gray-300 focus:border-gray-400 rounded-lg">
                    <SelectValue placeholder="Select type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Types</SelectItem>
                    {OPERATION_LOG_TYPES.map((type) => (
                      <SelectItem key={type} value={type}>
                        <span className="capitalize">{type}</span>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Date Range */}
              <div className="space-y-3">
                <label className="text-sm font-medium text-gray-700">
                  Date Range
                </label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        'w-full justify-start text-left font-normal border-gray-300 hover:border-gray-400 rounded-lg',
                        !filters.dateRange?.from && 'text-gray-500',
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {filters.dateRange?.from ? (
                        filters.dateRange.to ? (
                          <>
                            {format(filters.dateRange.from, 'MMM dd')} -{' '}
                            {format(filters.dateRange.to, 'MMM dd')}
                          </>
                        ) : (
                          format(filters.dateRange.from, 'MMM dd, yyyy')
                        )
                      ) : (
                        'Select date range'
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="range"
                      defaultMonth={filters.dateRange?.from}
                      selected={{
                        from: filters.dateRange?.from,
                        to: filters.dateRange?.to,
                      }}
                      onSelect={(range) =>
                        onFilterChange({ ...filters, dateRange: range || {} })
                      }
                      numberOfMonths={2}
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>
          </PopoverContent>
        </Popover>

        {/* Column Visibility */}
        <ColumnVisibilityToggle
          columns={ALL_COLUMNS}
          columnVisibility={tableState.columnVisibility}
          onColumnVisibilityChange={(columnVisibility) => {
            const newVisibleColumns = ALL_COLUMNS.filter(
              (col) => columnVisibility[col.key],
            ).map((col) => col.key);

            onTableStateChange({
              ...tableState,
              columnVisibility,
              visibleColumns: newVisibleColumns,
            });
          }}
        />
      </div>

      {/* Active Filters Display */}
      {activeFiltersCount > 0 && (
        <div className="flex items-center gap-2 flex-wrap mt-4 sm:mt-0">
          {filters.status && (
            <Badge
              variant="secondary"
              className="h-7 text-xs bg-emerald-100 text-emerald-700 border border-emerald-200 rounded-lg"
            >
              Status: {filters.status}
              <Button
                variant="ghost"
                size="sm"
                onClick={() =>
                  onFilterChange({ ...filters, status: undefined })
                }
                className="ml-1 h-auto p-0 hover:bg-transparent"
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          )}
          {filters.operationType && (
            <Badge
              variant="secondary"
              className="h-7 text-xs bg-blue-100 text-blue-700 border border-blue-200 rounded-lg"
            >
              Type: {filters.operationType}
              <Button
                variant="ghost"
                size="sm"
                onClick={() =>
                  onFilterChange({ ...filters, operationType: undefined })
                }
                className="ml-1 h-auto p-0 hover:bg-transparent"
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          )}
          {filters.dateRange?.from && (
            <Badge
              variant="secondary"
              className="h-7 text-xs bg-amber-100 text-amber-700 border border-amber-200 rounded-lg"
            >
              Date: {format(filters.dateRange.from, 'MMM dd')}
              {filters.dateRange.to &&
                ` - ${format(filters.dateRange.to, 'MMM dd')}`}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onFilterChange({ ...filters, dateRange: {} })}
                className="ml-1 h-auto p-0 hover:bg-transparent"
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          )}
        </div>
      )}
    </div>
  );
}
