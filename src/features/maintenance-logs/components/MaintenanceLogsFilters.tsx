import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Input } from '@/components/ui/input';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { cn } from '@/lib/utils';
import { format } from 'date-fns';
import { CalendarIcon, Search } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useEffect, useState } from 'react';
import { OPERATION_LOG_TYPES } from '../schemas/create-maintenance-log';
import type { MaintenanceLogsFilters } from '../types/table';
import { MaintenanceTypeBadge } from './MaintenanceTypeBadge';

interface MaintenanceLogsFiltersProps {
  onFilterChange: (filters: MaintenanceLogsFilters) => void;
  filters: MaintenanceLogsFilters;
}

export function MaintenanceLogsFilters({
  onFilterChange,
  filters,
}: MaintenanceLogsFiltersProps) {
  const t = useTranslations('pages.maintenanceLogs');
  const [debouncedSearch, setDebouncedSearch] = useState(filters.search);

  const handleOperationTypeChange = (value: string) => {
    onFilterChange({
      ...filters,
      operationType:
        value === 'all'
          ? undefined
          : (value as (typeof OPERATION_LOG_TYPES)[number]),
    });
  };

  const handleStatusChange = (value: string) => {
    onFilterChange({
      ...filters,
      status:
        value === 'all'
          ? undefined
          : (value as (typeof import('../schemas/create-maintenance-log').MAINTENANCE_STATUS)[number]),
    });
  };

  const handleDateRangeChange = (dateRange: { from?: Date; to?: Date }) => {
    onFilterChange({
      ...filters,
      dateRange,
    });
  };

  const handleSearchChange = (search: string) => {
    setDebouncedSearch(search);
  };

  // Helper function to check if any filters are active
  const hasActiveFilters =
    filters.operationType ||
    filters.status ||
    filters.dateRange?.from ||
    filters.search;

  const clearAllFilters = () => {
    onFilterChange({
      operationType: undefined,
      status: undefined,
      dateRange: undefined,
      search: '',
    });
    setDebouncedSearch('');
  };

  // Debounce search
  useEffect(() => {
    const timer = setTimeout(() => {
      if (debouncedSearch !== filters.search) {
        onFilterChange({
          ...filters,
          search: debouncedSearch || '',
        });
      }
    }, 300);

    return () => clearTimeout(timer);
  }, [debouncedSearch, filters, onFilterChange]);

  return (
    <div
      className="p-6 space-y-6"
      role="search"
      aria-label="Maintenance logs filters"
    >
      {/* Modern Filter Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <div
            className="p-3 rounded-2xl bg-gradient-to-br from-blue-500 to-indigo-600 text-white shadow-lg"
            aria-hidden="true"
          >
            <Search className="h-5 w-5" />
          </div>
          <div>
            <h3
              id="filters-heading"
              className="text-xl font-bold text-gray-900"
            >
              Smart Filters
            </h3>
            <p
              className="text-sm text-gray-600"
              aria-live="polite"
              aria-atomic="true"
            >
              {hasActiveFilters
                ? `${Object.values(filters).filter(Boolean).length} filter${Object.values(filters).filter(Boolean).length !== 1 ? 's' : ''} applied`
                : 'No filters applied'}
            </p>
          </div>
        </div>
        {hasActiveFilters && (
          <Button
            variant="outline"
            size="sm"
            onClick={clearAllFilters}
            className="text-gray-600 hover:text-gray-900 border-gray-200 hover:bg-gray-50 transition-all duration-200"
            aria-label="Clear all active filters"
          >
            Clear all filters
          </Button>
        )}
      </div>

      {/* Active Filter Chips */}
      {hasActiveFilters && (
        <div
          className="flex flex-wrap gap-2 p-4 bg-blue-50/50 rounded-xl border border-blue-100"
          role="region"
          aria-label="Active filters"
        >
          {filters.operationType && (
            <div className="inline-flex items-center gap-2 px-3 py-1.5 bg-white rounded-lg border border-blue-200 text-sm font-medium text-blue-700">
              <span>Type: {filters.operationType}</span>
              <button
                onClick={() => handleOperationTypeChange('all')}
                className="text-blue-500 hover:text-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 rounded"
                aria-label={`Remove operation type filter: ${filters.operationType}`}
                type="button"
              >
                <span aria-hidden="true">×</span>
              </button>
            </div>
          )}
          {filters.status && (
            <div className="inline-flex items-center gap-2 px-3 py-1.5 bg-white rounded-lg border border-blue-200 text-sm font-medium text-blue-700">
              <span>Status: {filters.status}</span>
              <button
                onClick={() => handleStatusChange('all')}
                className="text-blue-500 hover:text-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 rounded"
                aria-label={`Remove status filter: ${filters.status}`}
                type="button"
              >
                <span aria-hidden="true">×</span>
              </button>
            </div>
          )}
          {filters.search && (
            <div className="inline-flex items-center gap-2 px-3 py-1.5 bg-white rounded-lg border border-blue-200 text-sm font-medium text-blue-700">
              <span>Search: &ldquo;{filters.search}&rdquo;</span>
              <button
                onClick={() => {
                  setDebouncedSearch('');
                  onFilterChange({ ...filters, search: '' });
                }}
                className="text-blue-500 hover:text-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 rounded"
                aria-label={`Remove search filter: ${filters.search}`}
                type="button"
              >
                <span aria-hidden="true">×</span>
              </button>
            </div>
          )}
          {filters.dateRange?.from && (
            <div className="inline-flex items-center gap-2 px-3 py-1.5 bg-white rounded-lg border border-blue-200 text-sm font-medium text-blue-700">
              <span>
                Date: {format(filters.dateRange.from, 'MMM dd')}
                {filters.dateRange.to &&
                  ` - ${format(filters.dateRange.to, 'MMM dd')}`}
              </span>
              <button
                onClick={() => handleDateRangeChange({})}
                className="text-blue-500 hover:text-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 rounded"
                aria-label="Remove date range filter"
                type="button"
              >
                <span aria-hidden="true">×</span>
              </button>
            </div>
          )}
        </div>
      )}

      {/* Enhanced Filter Controls */}
      <fieldset className="grid gap-6 sm:grid-cols-2 lg:grid-cols-4">
        <legend className="sr-only">Filter maintenance logs by criteria</legend>

        {/* Operation Type Filter */}
        <div className="space-y-3">
          <label
            htmlFor="operation-type-select"
            className="text-sm font-semibold text-gray-800 flex items-center gap-2"
          >
            <div
              className="w-2 h-2 bg-blue-500 rounded-full"
              aria-hidden="true"
            ></div>
            Operation Type
          </label>
          <Select
            value={filters.operationType || 'all'}
            onValueChange={handleOperationTypeChange}
          >
            <SelectTrigger
              id="operation-type-select"
              className="w-full bg-white border-gray-200 hover:border-blue-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-100 transition-all duration-200 rounded-xl h-12 shadow-sm"
              aria-label="Select operation type filter"
            >
              <SelectValue placeholder="Select operation type..." />
            </SelectTrigger>
            <SelectContent className="rounded-xl border-gray-200 shadow-xl">
              <SelectItem value="all" className="rounded-lg">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 rounded-lg bg-gray-100 flex items-center justify-center">
                    <span className="text-xs font-medium">All</span>
                  </div>
                  <span className="font-medium">All Types</span>
                </div>
              </SelectItem>
              {OPERATION_LOG_TYPES.map((type) => (
                <SelectItem
                  key={type}
                  value={type}
                  className="flex items-center gap-3 rounded-lg"
                >
                  <div className="flex items-center gap-3">
                    <MaintenanceTypeBadge type={type} />
                    <span className="font-medium">
                      {t(`operationType.${type}`, { default: type })}
                    </span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Status Filter */}
        <div className="space-y-3">
          <label className="text-sm font-semibold text-gray-800 flex items-center gap-2">
            <div className="w-2 h-2 bg-emerald-500 rounded-full"></div>
            Status
          </label>
          <Select
            value={filters.status || 'all'}
            onValueChange={handleStatusChange}
          >
            <SelectTrigger className="w-full bg-white border-gray-200 hover:border-emerald-300 focus:border-emerald-500 focus:ring-2 focus:ring-emerald-100 transition-all duration-200 rounded-xl h-12 shadow-sm">
              <SelectValue placeholder="Select status..." />
            </SelectTrigger>
            <SelectContent className="rounded-xl border-gray-200 shadow-xl">
              <SelectItem value="all" className="rounded-lg">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 rounded-lg bg-gray-100 flex items-center justify-center">
                    <span className="text-xs font-medium">All</span>
                  </div>
                  <span className="font-medium">All Status</span>
                </div>
              </SelectItem>
              {(['fully function', 'broken'] as const).map((status) => (
                <SelectItem
                  key={status}
                  value={status}
                  className="flex items-center gap-3 rounded-lg"
                >
                  <div className="flex items-center gap-3">
                    <div
                      className={`w-3 h-3 rounded-full ${
                        status === 'fully function'
                          ? 'bg-emerald-500'
                          : 'bg-red-500'
                      }`}
                    />
                    <span
                      className={`inline-flex items-center px-3 py-1 rounded-lg text-xs font-semibold ${
                        status === 'fully function'
                          ? 'bg-emerald-50 text-emerald-700 border border-emerald-200'
                          : 'bg-red-50 text-red-700 border border-red-200'
                      }`}
                    >
                      {t(`status.${status}`, { default: status })}
                    </span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Date Range Filter */}
        <div className="space-y-3">
          <label className="text-sm font-semibold text-gray-800 flex items-center gap-2">
            <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
            Date Range
          </label>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className={cn(
                  'w-full justify-start text-left font-medium bg-white border-gray-200 hover:border-purple-300 focus:border-purple-500 focus:ring-2 focus:ring-purple-100 transition-all duration-200 rounded-xl h-12 shadow-sm',
                  !filters.dateRange && 'text-gray-500',
                )}
              >
                <div className="flex items-center gap-3">
                  <div className="p-1.5 rounded-lg bg-purple-50">
                    <CalendarIcon className="h-4 w-4 text-purple-600" />
                  </div>
                  <span>
                    {filters.dateRange?.from ? (
                      filters.dateRange.to ? (
                        <>
                          {format(filters.dateRange.from, 'MMM dd')} -{' '}
                          {format(filters.dateRange.to, 'MMM dd, y')}
                        </>
                      ) : (
                        format(filters.dateRange.from, 'MMM dd, y')
                      )
                    ) : (
                      'Select date range...'
                    )}
                  </span>
                </div>
              </Button>
            </PopoverTrigger>
            <PopoverContent
              className="w-auto p-0 rounded-xl border-gray-200 shadow-xl"
              align="start"
            >
              <Calendar
                mode="range"
                defaultMonth={filters.dateRange?.from}
                selected={{
                  from: filters.dateRange?.from,
                  to: filters.dateRange?.to,
                }}
                onSelect={(range) => handleDateRangeChange(range || {})}
                numberOfMonths={2}
                className="rounded-xl"
              />
            </PopoverContent>
          </Popover>
        </div>

        {/* Search Filter */}
        <div className="space-y-3">
          <label className="text-sm font-semibold text-gray-800 flex items-center gap-2">
            <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
            Search
          </label>
          <div className="relative">
            <div className="absolute left-4 top-1/2 transform -translate-y-1/2 p-1.5 rounded-lg bg-orange-50">
              <Search className="h-4 w-4 text-orange-600" />
            </div>
            <Input
              placeholder="Search descriptions, contractors, people in charge, types, status..."
              value={debouncedSearch || ''}
              onChange={(e) => handleSearchChange(e.target.value)}
              className="w-full pl-14 pr-4 bg-white border-gray-200 hover:border-orange-300 focus:border-orange-500 focus:ring-2 focus:ring-orange-100 transition-all duration-200 rounded-xl h-12 shadow-sm font-medium placeholder:text-gray-400"
            />
            {debouncedSearch && (
              <button
                onClick={() => {
                  setDebouncedSearch('');
                  onFilterChange({ ...filters, search: '' });
                }}
                className="absolute right-4 top-1/2 transform -translate-y-1/2 p-1 rounded-lg hover:bg-gray-100 transition-colors"
              >
                <span className="text-gray-400 hover:text-gray-600">×</span>
              </button>
            )}
          </div>
        </div>
      </fieldset>

      {/* Quick Filter Presets */}
      <div className="pt-4 border-t border-gray-100">
        <div className="flex items-center justify-between mb-4">
          <h4 className="text-sm font-semibold text-gray-800">Quick Filters</h4>
          <span className="text-xs text-gray-500">Popular combinations</span>
        </div>
        <div
          className="flex flex-wrap gap-2"
          role="group"
          aria-label="Quick filter presets"
        >
          <button
            onClick={() => {
              handleStatusChange('broken');
              handleOperationTypeChange('all');
            }}
            className="inline-flex items-center gap-2 px-4 py-2 bg-red-50 hover:bg-red-100 text-red-700 rounded-xl border border-red-200 transition-all duration-200 text-sm font-medium focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
            aria-label="Filter for critical issues (broken status)"
            type="button"
          >
            <div
              className="w-2 h-2 bg-red-500 rounded-full"
              aria-hidden="true"
            ></div>
            Critical Issues
          </button>
          <button
            onClick={() => {
              handleOperationTypeChange('daily logs');
              handleStatusChange('all');
            }}
            className="inline-flex items-center gap-2 px-4 py-2 bg-blue-50 hover:bg-blue-100 text-blue-700 rounded-xl border border-blue-200 transition-all duration-200 text-sm font-medium focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            aria-label="Filter for daily logs operation type"
            type="button"
          >
            <div
              className="w-2 h-2 bg-blue-500 rounded-full"
              aria-hidden="true"
            ></div>
            Daily Logs
          </button>
          <button
            onClick={() => {
              handleStatusChange('fully function');
              handleOperationTypeChange('all');
            }}
            className="inline-flex items-center gap-2 px-4 py-2 bg-emerald-50 hover:bg-emerald-100 text-emerald-700 rounded-xl border border-emerald-200 transition-all duration-200 text-sm font-medium focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2"
            aria-label="Filter for working systems (fully functional status)"
            type="button"
          >
            <div
              className="w-2 h-2 bg-emerald-500 rounded-full"
              aria-hidden="true"
            ></div>
            Working Systems
          </button>
        </div>
      </div>
    </div>
  );
}
