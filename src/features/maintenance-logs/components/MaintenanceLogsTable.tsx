import { TableSection } from '@/components/table-section';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { format } from 'date-fns';
import { Calendar, MoreHorizontal, User } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useState } from 'react';
import type {
  MAINTENANCE_STATUS,
  OPERATION_LOG_TYPES,
} from '../schemas/create-maintenance-log';
import type {
  MaintenanceLogsTableProps as BaseMaintenanceLogsTableProps,
  MaintenanceLogsTableColumns,
  MaintenanceLogsType,
} from '../types/table';
import { DEFAULT_COLUMNS } from '../types/table';
import { MaintenanceLogDetailModal } from './MaintenanceLogDetailModal';
import { MaintenanceTypeBadge } from './MaintenanceTypeBadge';

// Extend props to allow explicit pagination control
export interface MaintenanceLogsTableProps
  extends BaseMaintenanceLogsTableProps {
  currentPage?: number;
  totalPages?: number;
  onPageChange?: (page: number) => void;
  pageSize?: number;
  onPageSizeChange?: (size: number) => void;
}

function renderCell(
  row: MaintenanceLogsType,
  key: string | number | symbol,
  t?: ReturnType<typeof useTranslations>,
) {
  const columnKey = String(key);
  const value = (row as unknown as Record<string, unknown>)[columnKey];
  if (columnKey === 'operation_log_type') {
    return (
      <MaintenanceTypeBadge
        type={value as (typeof OPERATION_LOG_TYPES)[number]}
      />
    );
  }
  if (columnKey === 'status') {
    const statusConfig = {
      'fully function': {
        variant: 'default' as const,
        className:
          'bg-gradient-to-r from-emerald-50 to-green-50 text-emerald-700 border border-emerald-200/60 hover:from-emerald-100 hover:to-green-100 shadow-sm font-semibold',
        icon: '✓',
      },
      broken: {
        variant: 'destructive' as const,
        className:
          'bg-gradient-to-r from-red-50 to-rose-50 text-red-700 border border-red-200/60 hover:from-red-100 hover:to-rose-100 shadow-sm font-semibold',
        icon: '✕',
      },
    };
    const statusValue = value as (typeof MAINTENANCE_STATUS)[number];
    const config = statusConfig[statusValue as keyof typeof statusConfig] || {
      variant: 'outline' as const,
      className:
        'bg-gradient-to-r from-gray-50 to-slate-50 text-gray-700 border border-gray-200/60 shadow-sm',
      icon: '?',
    };
    return (
      <Badge
        variant={config.variant}
        className={cn(
          'px-3 py-1.5 text-xs transition-all duration-200',
          config.className,
        )}
      >
        <span className="mr-1.5">{config.icon}</span>
        {t
          ? t(`status.${statusValue}`, { default: statusValue })
          : String(statusValue)}
      </Badge>
    );
  }
  if (columnKey === 'log_date') {
    // Use created_at for full timestamp instead of log_date which only contains date
    const createdAtValue = (row as unknown as Record<string, unknown>)[
      'created_at'
    ];
    const dateValue = createdAtValue ? String(createdAtValue) : String(value);
    return (
      <div className="flex items-center space-x-3 text-sm">
        <div className="p-2 rounded-lg bg-gradient-to-br from-blue-50 to-indigo-50 border border-blue-100/50">
          <Calendar className="h-4 w-4 text-blue-600" />
        </div>
        <div className="flex flex-col">
          <span className="font-semibold text-slate-800">
            {format(new Date(dateValue), 'MMM dd, yyyy, hh:mm a')}
          </span>
          <span className="text-xs text-slate-500">
            {format(new Date(dateValue), 'EEEE')}
          </span>
        </div>
      </div>
    );
  }
  if (columnKey === 'contractor_name') {
    return (
      <div className="flex items-center space-x-3">
        <div className="p-2 rounded-xl bg-gradient-to-br from-purple-50 to-violet-50 border border-purple-100/50">
          <User className="h-4 w-4 text-purple-600" />
        </div>
        <div className="flex flex-col">
          <span className="font-semibold text-slate-800 text-sm">
            {String(value)}
          </span>
          <span className="text-xs text-slate-500">Contractor</span>
        </div>
      </div>
    );
  }
  if (columnKey === 'description') {
    return (
      <div className="max-w-sm">
        <p className="text-sm text-slate-700 line-clamp-2 leading-relaxed font-medium">
          {String(value)}
        </p>
      </div>
    );
  }
  if (columnKey === 'pma_number') {
    return value ? (
      <Badge
        variant="outline"
        className="font-mono text-xs bg-gradient-to-r from-slate-50 to-gray-50 text-slate-700 border-slate-200 hover:from-slate-100 hover:to-gray-100 transition-all duration-200 shadow-sm"
      >
        {String(value)}
      </Badge>
    ) : (
      <span className="text-xs text-slate-400 italic">No certificate</span>
    );
  }
  if (columnKey === 'created_by') {
    return (
      <div className="flex items-center space-x-3 text-sm">
        <div className="p-2 rounded-lg bg-gradient-to-br from-purple-50 to-indigo-50 border border-purple-100/50">
          <User className="h-4 w-4 text-purple-600" />
        </div>
        <div className="flex flex-col">
          <span className="font-semibold text-slate-800">
            {String(value || 'System')}
          </span>
          <span className="text-xs text-slate-500">Creator</span>
        </div>
      </div>
    );
  }
  if (columnKey === 'actions') {
    return (
      <Button
        variant="ghost"
        size="sm"
        className="h-9 w-9 p-0 rounded-xl hover:bg-gradient-to-r hover:from-slate-100 hover:to-gray-100 transition-all duration-200 group"
      >
        <MoreHorizontal className="h-4 w-4 text-slate-500 group-hover:text-slate-700 transition-colors" />
        <span className="sr-only">Open menu</span>
      </Button>
    );
  }
  return (
    <span className="text-sm font-medium text-slate-700">
      {String(value ?? '')}
    </span>
  );
}

const MaintenanceLogsTable = ({
  data,
  isLoading,
  totalItems: _totalItems,
  tableState,
  onTableStateChange,
  columns = DEFAULT_COLUMNS,
  currentPage,
  totalPages,
  onPageChange,
  pageSize,
  onPageSizeChange,
}: MaintenanceLogsTableProps) => {
  const t = useTranslations('pages.maintenanceLogs');
  const [selectedLog, setSelectedLog] = useState<MaintenanceLogsType | null>(
    null,
  );
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);

  const visibleColumns = (columns || DEFAULT_COLUMNS).filter(
    (col) => tableState.columnVisibility[col.key],
  ) as MaintenanceLogsTableColumns[];

  const handleRowClick = (row: MaintenanceLogsType) => {
    setSelectedLog(row);
    setIsDetailModalOpen(true);
  };

  return (
    <>
      <TableSection
        data={data}
        columns={visibleColumns}
        isLoading={isLoading}
        tableState={tableState}
        onTableStateChange={onTableStateChange}
        renderCell={(row: MaintenanceLogsType, key: string | number | symbol) =>
          renderCell(row, key, t)
        }
        currentPage={currentPage}
        totalPages={totalPages}
        onPageChange={onPageChange}
        pageSize={pageSize}
        onPageSizeChange={onPageSizeChange}
        onRowClick={handleRowClick}
      />

      <MaintenanceLogDetailModal
        maintenanceLog={selectedLog}
        open={isDetailModalOpen}
        onOpenChange={setIsDetailModalOpen}
      />
    </>
  );
};

export default MaintenanceLogsTable;
