import { cn } from '@/lib/utils';
import { useTranslations } from 'next-intl';
import {
  OPERATION_LOG_TYPES,
  OPERATION_TYPE_COLORS,
} from '../schemas/create-maintenance-log';

interface MaintenanceTypeBadgeProps {
  type: (typeof OPERATION_LOG_TYPES)[number];
  className?: string;
}

export function MaintenanceTypeBadge({
  type,
  className,
}: MaintenanceTypeBadgeProps) {
  const t = useTranslations('pages.maintenanceLogs.operationType');

  return (
    <span
      className={cn(
        'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border',
        OPERATION_TYPE_COLORS[type],
        className,
      )}
    >
      {t(type)}
    </span>
  );
}
