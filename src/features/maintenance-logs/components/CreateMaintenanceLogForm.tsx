'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { MaintenanceTypeBadge } from '@/features/maintenance-logs/components/MaintenanceTypeBadge';
import type { PMACertificate } from '@/features/pma-management';
import { Project } from '@/features/projects';
import { useToast } from '@/hooks/use-toast';
import { cn } from '@/lib/utils';
import { zodResolver } from '@hookform/resolvers/zod';
import { format } from 'date-fns';
import { AlertCircle } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useForm } from 'react-hook-form';
import {
  createMaintenanceLogSchema,
  OPERATION_LOG_TYPES,
  type CreateMaintenanceLogInput,
} from '../schemas/create-maintenance-log';

interface CreateMaintenanceLogFormProps {
  onSubmit: (data: CreateMaintenanceLogInput) => Promise<void>;
  projectData: Project;
  pmaCertificates: PMACertificate[];
  isSubmitting?: boolean;
}

export default function CreateMaintenanceLogForm({
  onSubmit,
  projectData,
  pmaCertificates = [],
  isSubmitting = false,
}: CreateMaintenanceLogFormProps) {
  const { toast } = useToast();
  const t = useTranslations('pages.maintenanceLogs.create.form');
  const tStatus = useTranslations('pages.maintenanceLogs.status');
  const _tCommon = useTranslations('common');

  const form = useForm<CreateMaintenanceLogInput>({
    resolver: zodResolver(createMaintenanceLogSchema),
    defaultValues: {
      log_date: new Date(),
      description: '',
      contractor_id: projectData?.contractor_id || '',
      pma_id: pmaCertificates[0]?.id || '',
    },
  });

  const handleSubmit = async (data: CreateMaintenanceLogInput) => {
    try {
      await onSubmit(data);
      form.reset();
    } catch (error) {
      toast({
        title: t('../../errors.title'),
        description:
          error instanceof Error
            ? error.message
            : t('../../errors.failedToCreate'),
        variant: 'destructive',
      });
      // Also log for debugging
      console.error('Form submission error:', error);
    }
  };

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(handleSubmit)}
        className="space-y-8 bg-white/60 backdrop-blur-sm rounded-xl border border-slate-200/60 p-6 shadow-xl shadow-slate-200/20"
      >
        {/* Log Date (read-only) */}
        <FormField
          control={form.control}
          name="log_date"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t('logDate')}</FormLabel>
              <FormControl>
                <Input
                  value={format(field.value, 'PPpp')}
                  disabled
                  name={field.name}
                  onBlur={field.onBlur}
                  ref={field.ref}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Operation Type */}
        <FormField
          control={form.control}
          name="operation_log_type"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t('operationType')}</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder={t('selectOperationType')} />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {OPERATION_LOG_TYPES.map((type) => (
                    <SelectItem
                      key={type}
                      value={type}
                      className="flex items-center gap-2"
                    >
                      <MaintenanceTypeBadge type={type} />
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Status */}
        <FormField
          control={form.control}
          name="status"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t('status')}</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder={t('selectStatus')} />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {(['fully function', 'broken'] as const).map((status) => (
                    <SelectItem
                      key={status}
                      value={status}
                      className="flex items-center gap-2"
                    >
                      <span
                        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${
                          status === 'fully function'
                            ? 'bg-green-50 text-green-700 border-green-100'
                            : 'bg-red-50 text-red-700 border-red-100'
                        }`}
                      >
                        {tStatus(status)}
                      </span>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* PMA Certificate */}
        <FormField
          control={form.control}
          name="pma_id"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t('pmaCertificate')}</FormLabel>
              <Select onValueChange={field.onChange} value={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder={t('selectPmaCertificate')} />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {pmaCertificates.length === 0 ? (
                    <SelectItem value="no-certificates" disabled>
                      {t('noPmaCertificates')}
                    </SelectItem>
                  ) : (
                    pmaCertificates?.map?.((cert) => {
                      const isExpired = new Date(cert.expiry_date) < new Date();
                      const isDraft = !!cert.pma_number?.startsWith('Draft-');
                      return (
                        <SelectItem
                          key={cert.id}
                          value={cert.id}
                          className={cn(
                            'flex items-center justify-between',
                            isExpired && 'text-red-500',
                            isDraft && 'text-amber-600',
                          )}
                        >
                          <div className="flex-1">
                            <div className="flex items-center gap-2">
                              <span className="font-medium">
                                {cert.pma_number ?? 'N/A'}
                              </span>
                              {isDraft && (
                                <span className="text-xs bg-amber-100 text-amber-700 px-1.5 py-0.5 rounded">
                                  {cert.status}
                                </span>
                              )}
                            </div>
                            {cert.location && (
                              <p className="text-xs text-muted-foreground">
                                📍 {cert.location}
                                {cert.state && `, ${cert.state}`}
                              </p>
                            )}
                          </div>
                          {isExpired && (
                            <AlertCircle className="h-4 w-4 text-red-500" />
                          )}
                        </SelectItem>
                      );
                    })
                  )}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Description */}
        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t('description')}</FormLabel>
              <FormControl>
                <Textarea
                  placeholder={t('descriptionPlaceholder')}
                  className="min-h-[120px]"
                  maxLength={500}
                  {...field}
                />
              </FormControl>
              <div className="flex justify-between items-start mt-2">
                <FormMessage />
                <span
                  className={cn(
                    'text-xs font-medium',
                    field.value.length > 500 && 'text-red-500',
                    field.value.length < 10 &&
                      field.value.length > 0 &&
                      'text-amber-500',
                    field.value.length >= 10 &&
                      field.value.length <= 500 &&
                      'text-green-600',
                    field.value.length === 0 && 'text-muted-foreground',
                  )}
                >
                  {field.value.length}/500
                </span>
              </div>
              {field.value.length < 10 && field.value.length > 0 && (
                <p className="text-xs text-amber-600 mt-1">
                  {t('descriptionMinLength', { min: 10 })}
                </p>
              )}
              {field.value.length > 450 && (
                <p className="text-xs text-amber-600 mt-1">
                  {t('descriptionNearLimit', {
                    remaining: 500 - field.value.length,
                  })}
                </p>
              )}
            </FormItem>
          )}
        />

        {/* Submit Button */}
        <div className="flex justify-end">
          <Button
            type="submit"
            size="lg"
            disabled={isSubmitting}
            className="bg-gradient-to-r from-blue-500 to-indigo-600 text-white hover:from-blue-600 hover:to-indigo-700"
          >
            {isSubmitting ? t('submitting') : t('submit')}
          </Button>
        </div>
      </form>
    </Form>
  );
}
