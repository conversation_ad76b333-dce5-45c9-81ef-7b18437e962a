import type { SupabaseClient } from '@supabase/supabase-js';
import { OPERATION_LOG_TYPES } from '../schemas/create-maintenance-log';
import type {
  MaintenanceLogsType,
  MaintenanceTableState,
} from '../types/table';

/**
 * Safely converts any value to a string
 */
export function safeString(value: unknown): string {
  if (value === null || value === undefined) return '';
  return String(value).trim();
}

/**
 * Normalizes search term by removing extra spaces and special characters
 */
export function normalizeSearchTerm(term: string): string {
  return term
    .trim()
    .replace(/\s+/g, ' ') // Replace multiple spaces with single space
    .replace(/[%_]/g, '\\$&'); // Escape SQL LIKE wildcards
}

/**
 * Builds search conditions for a given term across multiple fields
 */
export function buildSearchConditions(searchTerm: string): string {
  const normalizedTerm = normalizeSearchTerm(searchTerm);

  // Define all searchable fields
  const searchableFields = [
    'description',
    'person_in_charge_name',
    'person_in_charge_phone',
    'contractors.name',
    'operation_log_type',
    'status',
    'pma_certificates.pma_number',
  ];

  // Build OR conditions for each field
  return searchableFields
    .map((field) => `${field}.ilike.%${normalizedTerm}%`)
    .join(',');
}

/**
 * Type guard for operation log types
 */
export function isValidOperationType(
  type: unknown,
): type is (typeof OPERATION_LOG_TYPES)[number] {
  if (typeof type !== 'string') return false;
  return OPERATION_LOG_TYPES.includes(
    type as (typeof OPERATION_LOG_TYPES)[number],
  );
}

/**
 * Type guard for maintenance status
 */
export function isValidMaintenanceStatus(
  status: unknown,
): status is 'fully function' | 'broken' {
  if (typeof status !== 'string') return false;
  return ['fully function', 'broken'].includes(status);
}

/**
 * Builds a base query with filters applied
 *
 * Search functionality covers the following fields:
 * - description: Log description content
 * - person_in_charge_name: Name of person in charge
 * - person_in_charge_phone: Phone number of person in charge
 * - contractors.name: Contractor company name (via join)
 * - operation_log_type: Type of operation (daily logs, second schedule, mantrap)
 * - status: Log status (fully function, broken)
 * - pma_certificates.pma_number: PMA certificate number (via join)
 *
 * @param query - The base Supabase query object
 * @param filters - The filters to apply including search term
 * @param options - Query options including count flag
 * @returns The filtered query object
 */
export function buildQuery(
  query: ReturnType<SupabaseClient['from']>,
  filters: MaintenanceTableState['filters'],
  options: { withCount?: boolean } = { withCount: true },
) {
  try {
    console.log('Building query with filters:', filters);

    // Base query with all fields including contractor name and created_by user via left joins
    let filteredQuery = query.select(
      `
            id,
            log_date,
            operation_log_type,
            contractor_id,
            person_in_charge_name,
            person_in_charge_phone,
            description,
            created_at,
            created_by,
            project_id,
            pma_id,
            status,
            contractors (
                name
            ),
            pma_certificates (
                pma_number
            ),
            users!created_by (
                name
            )
        `,
      { count: options.withCount ? 'exact' : undefined },
    );

    // Apply search filter - search across multiple fields
    if (filters.search?.trim()) {
      const searchTerm = filters.search.trim();
      filteredQuery = filteredQuery.or(buildSearchConditions(searchTerm));
    }

    // Apply operation type filter
    if (filters.operationType) {
      filteredQuery = filteredQuery.eq(
        'operation_log_type',
        filters.operationType,
      );
    }

    // Apply status filter
    if (filters.status) {
      filteredQuery = filteredQuery.eq('status', filters.status);
    }

    // Apply date range filter
    if (filters.dateRange?.from) {
      filteredQuery = filteredQuery.gte(
        'log_date',
        filters.dateRange.from.toISOString().split('T')[0],
      );
    }
    if (filters.dateRange?.to) {
      filteredQuery = filteredQuery.lte(
        'log_date',
        filters.dateRange.to.toISOString().split('T')[0],
      );
    }

    return filteredQuery;
  } catch (error) {
    console.error('Error building query:', error);
    throw error;
  }
}

/**
 * Transform database records to application data model
 */
export function transformData(
  data: Record<string, unknown>[],
): MaintenanceLogsType[] {
  return data.map((row) => {
    // Safely extract contractor name from join
    let contractorName = 'Unknown Contractor';
    const contractorsField = row.contractors;
    if (contractorsField && typeof contractorsField === 'object') {
      if (Array.isArray(contractorsField)) {
        const first = contractorsField[0] as Record<string, unknown>;
        if (first && typeof first.name === 'string') {
          contractorName = first.name;
        }
      } else {
        const obj = contractorsField as Record<string, unknown>;
        if (typeof obj.name === 'string') {
          contractorName = obj.name;
        }
      }
    }

    // Safely extract PMA number
    let pmaNumber: string | null = null;
    const pmaField = row.pma_certificates;
    if (pmaField && typeof pmaField === 'object') {
      if (Array.isArray(pmaField)) {
        const firstPma = pmaField[0] as Record<string, unknown>;
        if (firstPma && typeof firstPma.pma_number === 'string') {
          pmaNumber = firstPma.pma_number;
        }
      } else {
        const obj = pmaField as Record<string, unknown>;
        if (typeof obj.pma_number === 'string') {
          pmaNumber = obj.pma_number;
        }
      }
    }

    // Safely extract created_by user name
    let createdByName = 'System';
    const usersField = row.users;
    if (usersField && typeof usersField === 'object') {
      if (Array.isArray(usersField)) {
        const firstUser = usersField[0] as Record<string, unknown>;
        if (firstUser && typeof firstUser.name === 'string') {
          createdByName = firstUser.name;
        }
      } else {
        const obj = usersField as Record<string, unknown>;
        if (typeof obj.name === 'string') {
          createdByName = obj.name;
        }
      }
    }

    return {
      id: String(row.id),
      log_date: String(row.log_date),
      operation_log_type: isValidOperationType(row.operation_log_type)
        ? row.operation_log_type
        : 'daily logs', // Default to first new type
      contractor_id: safeString(row.contractor_id),
      contractor_name: safeString(contractorName),
      person_in_charge_name: safeString(row.person_in_charge_name),
      person_in_charge_phone: safeString(row.person_in_charge_phone),
      description: safeString(row.description),
      created_at: safeString(row.created_at || new Date().toISOString()),
      created_by: safeString(createdByName),
      project_id: safeString(row.project_id),
      pma_id: safeString(row.pma_id),
      pma_number: pmaNumber ? safeString(pmaNumber) : null,
      status: isValidMaintenanceStatus(row.status)
        ? row.status
        : 'fully function',
    };
  });
}

/**
 * Apply pagination to a query
 */
export function applyPagination<
  T extends { range: (from: number, to: number) => T },
>(query: T, pageIndex: number, pageSize: number) {
  return query.range(pageIndex * pageSize, (pageIndex + 1) * pageSize - 1);
}

/**
 * Apply sorting to a query
 */
export function applySorting<
  T extends { order: (column: string, options?: { ascending: boolean }) => T },
>(query: T, column?: string, direction: 'asc' | 'desc' = 'desc') {
  if (!column) {
    return query.order('log_date', { ascending: false });
  }

  return query.order(column, {
    ascending: direction === 'asc',
  });
}
