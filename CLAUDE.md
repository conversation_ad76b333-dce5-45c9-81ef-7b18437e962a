# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

SimPLE (Simple Project Lifecycle Enhancement) is a Next.js 15 application with Supabase authentication, built for project management with role-based access control and multi-language support.

## Essential Commands

### Development

```bash
pnpm dev                # Start development server with Turbopack
pnpm build              # Build for production
pnpm start              # Start production server
pnpm lint               # Run ESLint
pnpm type-check         # TypeScript type checking
pnpm validate           # Quick validation (type-check + lint + build)
pnpm validate:full      # Comprehensive validation via scripts/validate.sh
```

### Pre-Push Validation (MANDATORY)

Always run before pushing code:

```bash
pnpm validate:full
```

### Database & Supabase

```bash
supabase start          # Start local Supabase services
supabase stop           # Stop local services
supabase db reset       # Reset database with migrations

# Type generation
pnpm db:types:local     # Generate types from local DB
pnpm db:types:linked    # Generate types from linked remote DB

# Migrations
pnpm db:migration:new   # Create new migration
pnpm db:push            # Apply migrations to linked project
```

### Local Development URLs

- App: http://localhost:3000
- Supabase Studio: http://127.0.0.1:54323
- Email Testing: http://127.0.0.1:54324

## Architecture

### Core Structure

- **Feature-based architecture**: Code organized by business domain in `src/features/`
- **App Router**: Next.js 15 app directory structure with internationalization
- **Role-based access control**: Comprehensive RBAC system with middleware protection
- **Supabase integration**: Enhanced client with PKCE authentication

### Key Directories

- `src/features/`: Feature modules (auth, projects, maintenance-logs, etc.)
- `src/app/[locale]/`: Internationalized routes (English/Malay)
- `src/components/ui/`: Reusable UI components (Radix UI + Tailwind)
- `src/lib/`: Utility functions, Supabase clients, RBAC logic
- `src/types/`: TypeScript definitions including auto-generated database types

### Authentication Flow

- **PKCE authentication** with enhanced Supabase client
- **Middleware protection** for routes with role-based access
- **Onboarding flow** for new users
- **User roles**: admin, contractor, viewer with different permissions

### Database Integration

- **Type-safe database access** with auto-generated types from Supabase
- **Migration system** with scripts for local and production environments
- **Enhanced Supabase client** with consistent session management

### State Management

- **TanStack Query** for server state management
- **React Hook Form** with Zod validation for forms
- **Feature-specific hooks** for business logic

### UI Components

- **Radix UI primitives** with custom styling
- **Tailwind CSS** for styling with custom configuration
- **Shadcn/ui pattern** for component organization
- **Responsive design** with mobile-first approach

## Development Workflow

### Git Hooks

- **Pre-commit**: Runs lint-staged (prettier + eslint)
- **Pre-push**: Runs full validation
- **Commitlint**: Conventional commit messages required

### Testing

- Currently no test framework specified (test script exits with success)
- Check README or project structure before assuming test setup

### Code Quality

- **ESLint** with Next.js configuration
- **TypeScript** strict mode
- **Prettier** for code formatting
- **Husky** for git hooks

## Key Configuration Files

- `next.config.ts`: Next.js configuration
- `tailwind.config.js`: Tailwind CSS configuration
- `tsconfig.json`: TypeScript configuration
- `supabase/config.toml`: Supabase local configuration
- `messages/`: Internationalization files (en.json, ms.json)

## Important Notes

- Always run `pnpm validate:full` before pushing
- Use feature-based organization when adding new functionality
- Follow existing patterns for authentication and RBAC
- Generate database types after schema changes
- Respect internationalization structure for new routes
