# Email Customization Guide for SimPLE

This guide explains how email customization is configured for both local development and production using Supabase's built-in email service.

## Email Configuration Overview

### Local Development

- Uses **Inbucket** for email testing (no real emails sent)
- View captured emails at: `http://localhost:54324`
- All email templates are tested locally

### Production

- Uses **Supabase's built-in email service** (when `smtp.enabled = false`)
- Real emails are sent to users
- All custom templates and styling are preserved

## Email Templates Available

### 1. Password Recovery (`recovery.html` / `recovery_ms.html`)

- **Purpose**: Password reset emails
- **Trigger**: When user requests password reset
- **Languages**: English and Malay
- **Custom styling**: ✅ Implemented

### 2. Account Confirmation (`confirmation.html` / `confirmation_ms.html`)

- **Purpose**: Email verification for new accounts
- **Trigger**: When user signs up with email
- **Languages**: English and Malay
- **Custom styling**: ✅ Implemented

### 3. Invitation (`invite.html` / `invite_ms.html`)

- **Purpose**: User invitations to join platform
- **Trigger**: When admin invites new users
- **Languages**: English and Malay
- **Custom styling**: ✅ Implemented

### 4. Email Change Confirmation (`email_change.html`)

- **Purpose**: Confirm new email address changes
- **Trigger**: When user changes email address
- **Languages**: English (Malay version can be added)
- **Custom styling**: ✅ Implemented

### 5. Magic Link (`magic_link.html`)

- **Purpose**: Passwordless sign-in links
- **Trigger**: When user requests magic link login
- **Languages**: English (Malay version can be added)
- **Custom styling**: ✅ Implemented

## Email Template Variables

Each template has access to these Supabase variables:

- `{{ .ConfirmationURL }}` - The action URL for the email
- `{{ .Token }}` - OTP token (for recovery emails)
- `{{ .Email }}` - Recipient's email address
- `{{ .RedirectTo }}` - Redirect URL after action

## Customization Options

### 1. Sender Information

```toml
[auth.email.smtp]
admin_email = "<EMAIL>"  # From address
sender_name = "SimPLE"             # Sender name
```

### 2. Email Subjects

```toml
[auth.email.template.recovery]
subject = "Reset your SimPLE password"

[auth.email.template.invite]
subject = "Welcome to SimPLE - You've been invited!"
```

### 3. Custom HTML Templates

- All templates use consistent SimPLE branding
- Responsive design for mobile and desktop
- Accessibility considerations included
- Professional styling with brand colors

### 4. Template Structure

Each template includes:

- **Header**: SimPLE logo and title
- **Content**: Clear instructions and action buttons
- **Security Notice**: Important security information
- **Footer**: Contact and company information

## Production Deployment

### Supabase Dashboard Configuration

**Important**: Before deploying, manually configure these settings in your Supabase dashboard:

1. **Project Settings > Authentication > URL Configuration**
   - **Site URL**: Set to your actual domain (e.g., `https://simple-fe-stg-gwhvcad0ftgth9hb.southeastasia-01.azurewebsites.net` for staging)
   - **Redirect URLs**: Add your domain URLs to the allowed list
   - Leave SMTP disabled to use Supabase's built-in service

2. **Email Templates**
   - Email templates and styling are deployed automatically via `supabase config push`
   - Templates work with both custom SMTP and built-in email service

### CI/CD Template Deployment

**Important**: For email templates to work in hosted Supabase projects, you need two things:

1. **Include `supabase/` directory in deployments:**

```yaml
- name: Prepare standalone artifact
  run: |
    cp .env.local build/standalone/
    cp -r build/static build/standalone/build/static
    cp web.config build/standalone/
    cp startup.sh build/standalone/
    mkdir -p build/standalone/supabase  # ← Create supabase directory
    cp -r supabase/* build/standalone/supabase/  # ← Copy contents correctly
    chmod +x build/standalone/startup.sh
```

2. **Deploy email configuration to hosted Supabase:**

```yaml
- name: Deploy email templates and configuration
  run: |
    echo "Deploying email templates and config to staging..."
    supabase config push
```

Without the `supabase config push` step, hosted Supabase projects will use default templates instead of your custom ones.

### Domain Configuration (Optional)

For better email deliverability, you can:

1. **Configure Custom Domain**
   - Set up custom sender domain in Supabase dashboard
   - Add DNS records (SPF, DKIM, DMARC)

2. **Email Authentication**
   - Supabase handles email reputation automatically
   - Custom domains improve deliverability rates

## Testing Email Templates

### Local Testing

```bash
# Start Supabase locally
supabase start

# Trigger test emails through your app
# View emails at: http://localhost:54324
```

### Staging Testing

- Deploy to staging environment
- Test with real email addresses
- Verify all templates render correctly

## Template Maintenance

### Adding New Templates

1. Create HTML file in `/supabase/templates/`
2. Add configuration in `supabase/config.toml`
3. Test locally with Inbucket
4. Deploy to staging for testing

### Updating Existing Templates

1. Modify HTML files in `/supabase/templates/`
2. Test changes locally
3. Deploy through normal CI/CD process

### Multi-language Support

- Create `_ms.html` versions for Malay templates
- Use language-specific templates based on user preference
- Maintain consistent styling across languages

## Best Practices

1. **Consistent Branding**: All templates use SimPLE brand colors and fonts
2. **Mobile Responsive**: Templates work on all device sizes
3. **Accessibility**: Proper contrast ratios and semantic HTML
4. **Security**: Clear security notices in all authentication emails
5. **Localization**: Support for both English and Malay languages

## Troubleshooting

### Common Issues

1. **Templates not updating**
   - Ensure files are in correct `/supabase/templates/` directory
   - Check `config.toml` configuration
   - Restart local Supabase: `supabase stop && supabase start`

2. **Templates work locally but not in staging/production**
   - **Most common cause**: Email configuration not deployed to hosted Supabase project
   - **Solution**: Add `supabase config push` step to your CI/CD workflow after linking to project
   - **Secondary cause**: `supabase/` directory not included in deployment artifacts
   - Check CI/CD workflow includes: `mkdir -p build/standalone/supabase && cp -r supabase/* build/standalone/supabase/`
   - Verify deployment artifact contains `supabase/templates/` folder
   - **Path issue**: Ensure `content_path` in `config.toml` uses `supabase/templates/...` (without `./` prefix)
   - Redeploy after fixing the workflow or paths

3. **Emails not sending in production**
   - Verify `smtp.enabled = false` in production config
   - **Check Site URL configuration**: Ensure the Site URL in Supabase Dashboard matches your domain
   - **Check Redirect URLs**: Ensure your domain is in the allowed redirect URLs list
   - Check Supabase project authentication settings
   - Review email rate limits in configuration

4. **Styling issues**
   - Use inline CSS for maximum email client compatibility
   - Test with multiple email clients
   - Keep responsive design principles

### Support

For email-related issues:

1. Check Supabase authentication logs
2. Review email template configuration
3. Test with Inbucket locally
4. Contact Supabase support for deliverability issues
