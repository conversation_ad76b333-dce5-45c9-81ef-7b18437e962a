<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <title>Confirm Your SimPLE Account</title>
    <style>
      /* Reset styles for email client compatibility */
      body {
        margin: 0;
        padding: 0;
        font-family:
          -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
          'Helvetica Neue', Arial, sans-serif;
        line-height: 1.6;
        color: #1a1a1a;
        background-color: #f1f5f9;
      }
      table {
        border-collapse: collapse;
        width: 100%;
        max-width: 600px;
        margin: 0 auto;
      }
      .container {
        background-color: #ffffff;
        border-radius: 12px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        overflow: hidden;
        margin: 20px auto;
      }
      .header {
        padding: 40px 24px 32px;
        text-align: center;
        background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
        color: #ffffff;
      }
      .logo {
        font-size: 32px;
        font-weight: 800;
        margin: 0;
        letter-spacing: 0.5px;
      }
      .title {
        font-size: 24px;
        font-weight: 600;
        margin: 16px 0 8px;
      }
      .subtitle {
        font-size: 16px;
        opacity: 0.9;
        margin: 0;
      }
      .content {
        padding: 40px 24px;
        text-align: center;
      }
      .cta-button {
        display: inline-block;
        background: linear-gradient(90deg, #2563eb 0%, #1e40af 100%);
        color: #ffffff !important;
        padding: 14px 32px;
        border-radius: 8px;
        text-decoration: none;
        font-size: 16px;
        font-weight: 600;
        margin: 24px 0;
        box-shadow: 0 2px 8px rgba(37, 99, 235, 0.3);
        transition:
          transform 0.2s,
          box-shadow 0.2s;
      }
      .cta-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(37, 99, 235, 0.4);
      }
      .instructions {
        font-size: 15px;
        color: #374151;
        margin: 16px 0;
        max-width: 400px;
        margin-left: auto;
        margin-right: auto;
      }
      .fallback-link {
        font-size: 13px;
        color: #6b7280;
        word-break: break-all;
        display: block;
        margin-top: 16px;
      }
      .security-note {
        background-color: #dbeafe;
        border: 1px solid #bfdbfe;
        border-radius: 8px;
        padding: 16px;
        margin: 24px 24px 0;
        font-size: 14px;
        color: #1e40af;
        text-align: left;
        display: flex;
        align-items: flex-start;
      }
      .security-icon {
        margin-right: 12px;
        font-size: 20px;
        line-height: 1;
      }
      .footer {
        padding: 24px;
        font-size: 13px;
        color: #6b7280;
        text-align: center;
        background-color: #f8fafc;
        border-top: 1px solid #e2e8f0;
      }
      .footer a {
        color: #2563eb;
        text-decoration: none;
      }
      .footer a:hover {
        text-decoration: underline;
      }
      /* Accessibility enhancements */
      a:focus {
        outline: 2px solid #2563eb;
        outline-offset: 2px;
      }
      /* Responsive design */
      @media only screen and (max-width: 600px) {
        .container {
          border-radius: 0;
          margin: 0;
        }
        .header,
        .content,
        .footer {
          padding: 24px 16px;
        }
        .logo {
          font-size: 28px;
        }
        .title {
          font-size: 20px;
        }
        .subtitle {
          font-size: 14px;
        }
        .cta-button {
          padding: 12px 24px;
          font-size: 15px;
        }
        .instructions {
          font-size: 14px;
        }
        .security-note {
          margin: 16px 16px 0;
          font-size: 13px;
        }
      }
    </style>
  </head>
  <body>
    <table role="presentation">
      <tr>
        <td align="center">
          <div class="container">
            <div class="header">
              <h1 class="logo">SimPLE</h1>
              <h2 class="title">Welcome to SimPLE</h2>
              <p class="subtitle">One click to start your journey</p>
            </div>
            <div class="content">
              <p class="instructions">
                Thank you for joining SimPLE! Verify your email address to
                activate your account and unlock all features.
              </p>
              <a href="{{ .ConfirmationURL }}" class="cta-button" role="button"
                >Verify Your Email</a
              >
              <p class="instructions">
                If the button doesn’t work, copy and paste this link into your
                browser:
                <a href="{{ .ConfirmationURL }}" class="fallback-link"
                  >{{ .ConfirmationURL }}</a
                >
              </p>
              <div class="security-note">
                <span class="security-icon" role="img" aria-label="Lock icon"
                  >🔒</span
                >
                <div>
                  <strong>Secure Account:</strong> This link expires in 24 hours
                  to protect your account. Didn’t sign up? Ignore this email or
                  contact
                  <a href="mailto:<EMAIL>"><EMAIL></a>.
                </div>
              </div>
            </div>
          </div>
        </td>
      </tr>
    </table>
  </body>
</html>
