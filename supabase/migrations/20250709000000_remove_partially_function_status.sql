-- Remove 'partially function' from maintenance_status enum
-- This migration removes the 'partially function' option from the maintenance_status enum

-- First, update any existing records that have 'partially function' status
-- Change them to 'broken' status (you can change this to 'fully function' if preferred)
UPDATE maintenance_logs 
SET status = 'broken' 
WHERE status = 'partially function';

-- Create new enum without 'partially function'
CREATE TYPE maintenance_status_new AS ENUM ('fully function', 'broken');

-- Update the table to use the new enum
ALTER TABLE maintenance_logs 
ALTER COLUMN status TYPE maintenance_status_new 
USING status::text::maintenance_status_new;

-- Drop the old enum
DROP TYPE maintenance_status;

-- Rename the new enum to the original name
ALTER TYPE maintenance_status_new RENAME TO maintenance_status;

-- Update the comment to reflect the new enum values
COMMENT ON COLUMN maintenance_logs.status IS 'Status of the equipment after maintenance: fully function or broken';
